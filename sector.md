Retrieving Bucharest Sector Information via
 Google Geocoding API
 Bucharest, Romania is divided into six administrative sectors (numbered 1 through 6). Each sector is a
 municipal subdivision (e.g. Sector 1, Sector 2, … Sector 6) of the city . For example, Strada Valea
 Oltului 28 lies in Sector 6 of Bucharest. The goal is to determine the sector (district) of a given
 Bucharest address using the Google Maps Geocoding API in Python.
 1
 Google Geocoding API and Address Components
 Google’s Geocoding API returns a JSON with an array of address_components for each result. These
 components include elements like street number, route, neighborhood, city (locality), postal code, and
 various administrative levels. However, the API may not always explicitly return the sector for Bucharest
 addresses – often you will only see "București" as the city/locality, and possibly Municipiul București as a
 higher-level admin area, but no separate field for the sector number. Google’s geocoder omits certain
 sub-city divisions from the address components if they are not deemed part of the standard
 formatted address for that country . In other words, the JSON might list the street and city, but not
 mention “Sector 6” at all if Google doesn’t include it in the formatted address.
 2
 It’s important to note that the Geocoding API isn’t guaranteed to return a specific component for what
 you consider a city district – sometimes what you expect as a city or district might appear under a
 different type or not at all . (For instance, a borough might show up as a 
3
 instead of 
3
 sublocality_level_1
 locality in some locales .) Therefore, the sector might be hidden in an unexpected
 f
 ield, or missing entirely in the direct geocoding response.
 Checking the Geocoding Response for Sector Info
 The first step is to geocode the full address and inspect the address components in the JSON
 response. In Python, you can use the Google Maps Geocoding API via an HTTP request or a client library
 like 
googlemaps . After obtaining the result, iterate through 
result[0]["address_components"]
 to see if any component corresponds to the sector:
 • 
• 
Look for components of type 
sublocality or 
sublocality_level_1 , as Google might
 treat Bucharest sectors as sublocalities (first-order subdivisions below locality) in the data . 
Also check for types like 
administrative_area_level_2 or 
administrative_area_level_3 , since the sectors are administrative units of the city
 and might be represented at one of these levels in Google’s hierarchy. 
If one of the address components has 
3
 1
 "long_name": "Sector 6" (for example) with a type such as
 "political", "sublocality_level_1" or 
"administrative_area_level_2" , then you can
 directly extract that. In practice, though, the forward geocoding response often only shows
 "București" and does not include the sector number as a separate field (this appears to be the current
 behavior of Google’s geocoder for Romanian addresses). 
1
Using Reverse Geocoding to Obtain the Sector
 If the forward geocoding result doesn’t yield any sector information, a reliable workaround is to use
 reverse geocoding focused on administrative areas. The idea is:
 1. 
2. 
First, use the Geocoding API to get the latitude and longitude of the address.
 Then, call the Geocoding API in reverse, with the coordinates, asking specifically for an
 administrative area result (the sector).
 Google’s 
API 
allows 
restricting 
reverse 
geocode results by type. By setting
 result_type=administrative_area_level_2 (or possibly 
sublocality_level_1 ), we can ask
 Google to return the administrative division containing that point . In the context of Bucharest, the
 sectors are the relevant second-level administrative units below the city. For example, after geocoding
 “Strada Valea Oltului 28, București” to get its coordinates, you could do:
 4
 https://maps.googleapis.com/maps/api/geocode/json?
 latlng=44.4...,26.0...&result_type=administrative_area_level_2&key=YOUR_API_KEY
 This reverse geocode call should return a result like “Sector 6, Romania” (or “Sector 6, București,
 Romania”) in its formatted_address, with an address component for Sector 6. Using 
result_type
 4
 f
 ilters the results to only administrative areas, making it likely that the top result is the sector that
 contains the given coordinates . This approach was suggested by Google in similar cases where
 certain address levels (e.g. state or province) were missing from the forward geocode — performing a
 reverse lookup for that specific level yields the info .
 4
 How to implement: In Python, once you have the 
lat and 
make a second request (using the 
lng from the first geocoding result,
 googlemaps library or 
requests ) to reverse geocode. Specify
 result_type=["administrative_area_level_2"] 
in 
the
 googlemaps.Client.reverse_geocode method, or as a URL parameter. The response’s first result
 should include an address_component for the sector. (If Google internally considers sectors as a
 different level, you might also try 
sublocality_level_1 in the filter or in the component search.)
 Inferring the Sector from Postal Code
 5
 Another handy method is to use the postal code from the geocoding result (if available) to deduce the
 sector. Bucharest postal codes are structured such that the first two digits correspond to the sector
 number . All addresses in Sector 1 have postal codes starting with 
Sector 6 with 
06 . For example:
 • 
• 
• 
Sector 1 – postal codes 
01xxxx
 Sector 2 – postal codes 
02xxxx
 Sector 3 – postal codes 
03xxxx
 • 
• 
• 
Sector 4 – postal codes 
04xxxx
 Sector 5 – postal codes 
05xxxx
 Sector 6 – postal codes 
06xxxx
 5
 If the Geocoding API returns a 
01 , Sector 2 with 
02 , … up to
 postal_code component (it usually does for specific addresses), you
 can examine the first two digits of the postal code string. In code, this might look like: 
2
for comp in components:
 if "postal_code" in comp["types"]:
 postcode = comp["long_name"]
 if len(postcode) == 6 and postcode[:2].isdigit():
 sector_num = int(postcode[:2])
 if 1 <= sector_num <= 6:
 sector = f"Sector {sector_num}"
 For “Strada Valea Oltului 28”, the postal code is likely in the 06xxxx range (since it’s in Sector 6), so this
 method would correctly yield “Sector 6”. This trick relies on domain knowledge of Romanian postal
 codes, but it is a quick way to get the sector without additional API calls.
 Python Code Example: Retrieving the Sector for an Address
 Below is a Python example that uses the Google Maps Geocoding API (via the 
googlemaps library) to
 get the sector for “Strada Valea Oltului 28, București”. The code attempts to find the sector in the
 direct geocoding response and falls back to reverse geocoding if necessary. You would need to replace
 "YOUR_API_KEY" with your actual API key:
 import googlemaps
 # Initialize Google Maps client with your API key
 gmaps = googlemaps.Client(key="YOUR_API_KEY")
 address = "Strada Valea Oltului 28, București, Romania"
 # 1. Forward geocode the address to get coordinates and address components
 geocode_result = gmaps.geocode(address)
 sector = None
 if geocode_result:
 result = geocode_result[0]
 components = result["address_components"]
 # Check address components for a sector entry
 for comp in components:
 types = comp.get("types", [])
 if "sublocality_level_1" in types or "administrative_area_level_2" in
 types:
 if comp["long_name"].lower().startswith("sector"):
 sector = comp["long_name"]
 break
 # If not found, try inferring from postal code
 if sector is None:
 for comp in components:
 if "postal_code" in comp.get("types", []):
 postal = comp["long_name"]
 if len(postal) == 6 and postal[:2].isdigit():
 num = int(postal[:2])
 if 1 <= num <= 6:
 sector = f"Sector {num}"
 3
break
 # 2. If sector still not identified, use reverse geocoding as a fallback
 if sector is None:
 location = result["geometry"]["location"] # {'lat': ..., 'lng': ...}
 latlng = (location["lat"], location["lng"])
 reverse_results = gmaps.reverse_geocode(latlng,
 result_type=["administrative_area_level_2"])
 if reverse_results:
 # The reverse geocode result for admin_level_2 should be the 
sector
 for comp in reverse_results[0]["address_components"]:
 if "administrative_area_level_2" in comp.get("types", []):
 sector = comp["long_name"]
 break
 print(f"Address: {address}")
 print(f"Sector: {sector}")
 In this code, we:
 • 
• 
• 
• 
• 
Geocode the address to get its components and coordinates.
 Search the components for anything that looks like a sector (checking for types that might hold
 that info). We explicitly look for 
"Sector X" in a sublocality or admin_area component.
 If not found, we try to deduce the sector from the postal code prefix (as explained above).
 If that still fails, we take the latitude/longitude and perform a reverse geocode restricted to 
administrative_area_level_2 . The first result of this reverse lookup is expected to be the
 sector containing that point (e.g., Sector 6). We then extract the sector name from its address
 components.
 Finally, we print the sector. For the given example address, this should output “Sector 6”.
 Alternative Approaches and Data Sources
 If Google’s Geocoding API does not provide sector information directly, you can consider a few
 alternatives:
 • 
OpenStreetMap Nominatim API – The OSM geocoding service often returns addresses with the
 sector included. For example, a Nominatim query might return an address like “… Sector 2,
 Bucharest, … Romania” in its components . Nominatim’s 
6
 addressdetails will explicitly list
 the sector as a city district. This can be a free alternative (with usage limits).
 • 
• 
7
 Geocode.xyz or Other Geocoders – Third-party geocoders like Geocode.xyz leverage
 OpenStreetMap data and can identify Bucharest sectors. Geocode.xyz, for instance, recognizes
 sector boundaries: it can return an address such as “Strada X, Sector 6, București …” as one of
 the formatted outputs . These services might provide the sector in the output without extra
 steps.
 GIS/Polygon Method – As a more advanced solution, you can use known polygon boundaries
 of the Bucharest sectors to determine the sector from coordinates. Each sector is a defined
 polygon (administrative boundary) in geographic data . You could obtain a GeoJSON or
 8
 4
shapefile of Bucharest’s sector boundaries (for example, from OSM or a governmental open data
 portal). Using a Python GIS library (like 
shapely or 
geojson ), you can: 
• 
• 
• 
Geocode the address to get latitude/longitude.
 Load the sector polygons and find which polygon contains the coordinate.
 Return the sector name based on the match. 
This polygon method would reliably give you the sector, independent of any particular geocoding API’s
 quirks, as long as you have accurate boundary data. It is especially useful if you need to batch-process
 many addresses or want to double-check the sector result.
 In summary, the Google Maps Geocoding API by itself may not directly list the Bucharest sector in
 the address components. By combining strategies – scanning the geocode output, using reverse
 geocoding filters, or leveraging postal code patterns – you can derive the sector number for a given
 address. If Google’s data is insufficient, consider using OSM-based services or a custom GIS approach to
 get the sector-level detail. The provided Python example demonstrates how to integrate these steps to
 obtain “Sector 6” for the sample address using Google’s API and some auxiliary logic .
 4
 5
 1
 5
 Sector 6 (Bucharest) - Wikipedia
 https://en.wikipedia.org/wiki/Sector_6_(Bucharest)
 2
 4
 Google Maps geocoding API doesn't return administrative_area_level_1 - Stack Overflow
 https://stackoverflow.com/questions/43173003/google-maps-geocoding-api-doesnt-return-administrative-area-level-1
 3
 Geocoding request and response  |  Geocoding API  |  Google for Developers
 https://developers.google.com/maps/documentation/geocoding/requests-geocoding
 6
 Search - Nominatim 5.1.0 Manual
 https://nominatim.org/release-docs/latest/api/Search/
 7
 8
 Romania Municipiul Bucureşti, Bucureşti Bucharest Splaiul Independentei 060044 geoparsing,
 geocoding and batch geocoding.
 https://geocode.xyz/3045854983883
 5