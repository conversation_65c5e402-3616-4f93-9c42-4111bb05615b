#!/usr/bin/env python3
"""
GitHub Actions Setup Helper Script

This script helps validate and configure the SmartBill automation for GitHub Actions deployment.
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional


class GitHubActionsSetup:
    """Helper class for GitHub Actions setup and validation."""
    
    def __init__(self):
        self.repo_root = Path(__file__).parent.parent
        self.required_secrets = [
            'SMARTBILL_USERNAME',
            'SMARTBILL_PASSWORD',
            'GOOGLE_MAPS_API_KEY',
            'GOOGLE_SHEETS_CREDENTIALS',
            'GOOGLE_SHEETS_ID'
        ]
        self.optional_secrets = [
            'SMARTBILL_MFA_SECRET',
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHAT_ID',
            'WEBHOOK_URL'
        ]
    
    def validate_file_structure(self) -> bool:
        """Validate that all required files are present."""
        print("🔍 Validating file structure...")
        
        required_files = [
            '.github/workflows/smartbill-automation.yml',
            'src/main.py',
            'src/smartbill_automation.py',
            'src/geocoding_service.py',
            'src/config.py',
            'requirements.txt',
            'run_local.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.repo_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
                print(f"  ❌ Missing: {file_path}")
            else:
                print(f"  ✅ Found: {file_path}")
        
        if missing_files:
            print(f"\n❌ Missing {len(missing_files)} required files")
            return False
        
        print("✅ All required files are present")
        return True
    
    def validate_requirements(self) -> bool:
        """Validate requirements.txt has all necessary dependencies."""
        print("\n🔍 Validating requirements.txt...")
        
        required_packages = [
            'playwright',
            'googlemaps',
            'google-auth',
            'google-api-python-client',
            'pyotp',
            'requests'
        ]
        
        requirements_file = self.repo_root / 'requirements.txt'
        if not requirements_file.exists():
            print("❌ requirements.txt not found")
            return False
        
        with open(requirements_file, 'r') as f:
            requirements_content = f.read().lower()
        
        missing_packages = []
        for package in required_packages:
            if package not in requirements_content:
                missing_packages.append(package)
                print(f"  ❌ Missing: {package}")
            else:
                print(f"  ✅ Found: {package}")
        
        if missing_packages:
            print(f"\n❌ Missing {len(missing_packages)} required packages")
            return False
        
        print("✅ All required packages are present")
        return True
    
    def validate_workflow_file(self) -> bool:
        """Validate the GitHub Actions workflow file."""
        print("\n🔍 Validating workflow file...")
        
        workflow_file = self.repo_root / '.github/workflows/smartbill-automation.yml'
        if not workflow_file.exists():
            print("❌ Workflow file not found")
            return False
        
        with open(workflow_file, 'r') as f:
            workflow_content = f.read()
        
        # Check for required sections
        required_sections = [
            'workflow_dispatch',
            'schedule',
            'runs-on: ubuntu-latest',
            'playwright install',
            'SMARTBILL_USERNAME',
            'GOOGLE_MAPS_API_KEY'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in workflow_content:
                missing_sections.append(section)
                print(f"  ❌ Missing: {section}")
            else:
                print(f"  ✅ Found: {section}")
        
        if missing_sections:
            print(f"\n❌ Missing {len(missing_sections)} required sections")
            return False
        
        print("✅ Workflow file is properly configured")
        return True
    
    def generate_secrets_template(self) -> None:
        """Generate a template for GitHub secrets configuration."""
        print("\n📝 Generating secrets template...")
        
        template = {
            "required_secrets": {
                secret: f"<YOUR_{secret}>" for secret in self.required_secrets
            },
            "optional_secrets": {
                secret: f"<YOUR_{secret}>" for secret in self.optional_secrets
            },
            "instructions": {
                "setup_location": "GitHub Repository → Settings → Secrets and variables → Actions",
                "google_sheets_credentials": "Paste the entire JSON content from your Google service account key file",
                "google_sheets_id": "Extract from your Google Sheets URL: https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit",
                "telegram_setup": "Create a bot via @BotFather on Telegram to get bot token and chat ID"
            }
        }
        
        template_file = self.repo_root / 'github-secrets-template.json'
        with open(template_file, 'w') as f:
            json.dump(template, f, indent=2)
        
        print(f"✅ Secrets template saved to: {template_file}")
        print("📋 Use this template to configure your GitHub repository secrets")
    
    def validate_google_credentials_format(self, credentials_json: str) -> bool:
        """Validate Google Sheets credentials JSON format."""
        try:
            creds = json.loads(credentials_json)
            required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
            
            for field in required_fields:
                if field not in creds:
                    print(f"❌ Missing field in Google credentials: {field}")
                    return False
            
            if creds.get('type') != 'service_account':
                print("❌ Google credentials must be for a service account")
                return False
            
            print("✅ Google Sheets credentials format is valid")
            return True
            
        except json.JSONDecodeError:
            print("❌ Invalid JSON format for Google credentials")
            return False
    
    def check_environment_variables(self) -> None:
        """Check if environment variables are set for local testing."""
        print("\n🔍 Checking environment variables for local testing...")
        
        all_secrets = self.required_secrets + self.optional_secrets
        set_vars = []
        missing_vars = []
        
        for var in all_secrets:
            if os.environ.get(var):
                set_vars.append(var)
                print(f"  ✅ Set: {var}")
            else:
                missing_vars.append(var)
                print(f"  ❌ Missing: {var}")
        
        print(f"\n📊 Environment Variables Status:")
        print(f"  Set: {len(set_vars)}/{len(all_secrets)}")
        print(f"  Required missing: {len([v for v in missing_vars if v in self.required_secrets])}")
        print(f"  Optional missing: {len([v for v in missing_vars if v in self.optional_secrets])}")
    
    def generate_local_env_template(self) -> None:
        """Generate a .env template for local development."""
        print("\n📝 Generating .env template...")
        
        env_content = "# SmartBill Automation Environment Variables\n"
        env_content += "# Copy this file to .env and fill in your values\n\n"
        
        env_content += "# Required Secrets\n"
        for secret in self.required_secrets:
            env_content += f"{secret}=\n"
        
        env_content += "\n# Optional Secrets\n"
        for secret in self.optional_secrets:
            env_content += f"{secret}=\n"
        
        env_content += "\n# Local Development Settings\n"
        env_content += "HEADLESS=true\n"
        env_content += "MAX_RECORDS=5\n"
        env_content += "DRY_RUN=false\n"
        env_content += "ENVIRONMENT=development\n"
        
        env_file = self.repo_root / '.env.template'
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print(f"✅ Environment template saved to: {env_file}")
        print("📋 Copy to .env and fill in your values for local testing")
    
    def run_full_validation(self) -> bool:
        """Run complete validation suite."""
        print("🚀 Running GitHub Actions Setup Validation")
        print("=" * 60)
        
        validations = [
            self.validate_file_structure,
            self.validate_requirements,
            self.validate_workflow_file
        ]
        
        all_passed = True
        for validation in validations:
            if not validation():
                all_passed = False
        
        # Generate helpful templates
        self.generate_secrets_template()
        self.generate_local_env_template()
        self.check_environment_variables()
        
        print("\n" + "=" * 60)
        if all_passed:
            print("✅ All validations passed! Your repository is ready for GitHub Actions deployment.")
            print("\n📋 Next Steps:")
            print("  1. Configure secrets in GitHub repository settings")
            print("  2. Test manual workflow execution")
            print("  3. Configure scheduled execution")
            print("  4. Set up monitoring and notifications")
        else:
            print("❌ Some validations failed. Please fix the issues above before deploying.")
        
        return all_passed


def main():
    """Main function."""
    setup = GitHubActionsSetup()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'validate':
            setup.run_full_validation()
        elif command == 'secrets':
            setup.generate_secrets_template()
        elif command == 'env':
            setup.generate_local_env_template()
        elif command == 'check':
            setup.check_environment_variables()
        else:
            print(f"Unknown command: {command}")
            print("Available commands: validate, secrets, env, check")
    else:
        setup.run_full_validation()


if __name__ == "__main__":
    main()
