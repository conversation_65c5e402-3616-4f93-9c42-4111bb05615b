#!/usr/bin/env python3
"""
Test the sequential processing logic to verify the fixes.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.models import AddressRecord


def test_invoice_matching_logic():
    """Test the enhanced invoice matching logic."""
    
    print("🔍 Testing Invoice Matching Logic")
    print("=" * 60)
    
    # Create test records similar to what the automation processes
    test_records = [
        AddressRecord(
            client_id="SUN0298",
            invoice_id="invoice_1", 
            current_address="Matei Basarab,nr.100, bl.85 ,SC.2, et.4 Ap.47",
            current_city="Bucuresti",
            current_county="Bucuresti"
        ),
        AddressRecord(
            client_id="SUN0295",
            invoice_id="invoice_2",
            current_address="Strada Exemplu nr. 123",
            current_city="Bucuresti", 
            current_county="Bucuresti"
        ),
        AddressRecord(
            client_id="SUN0292",
            invoice_id="invoice_3",
            current_address="Bulevardul Test nr. 456",
            current_city="Bucuresti",
            current_county="Bucuresti"
        ),
        AddressRecord(
            client_id="SUN0290", 
            invoice_id="invoice_4",
            current_address="Aleea Demo nr. 789",
            current_city="Bucuresti",
            current_county="Bucuresti"
        )
    ]
    
    # Simulate table rows content (what would be found in the HTML table)
    mock_table_rows = [
        "SUN0298 Factura 2024-001 15.07.2024 100.00 RON Bucuresti",  # Row 1
        "SUN0295 Factura 2024-002 16.07.2024 200.00 RON Bucuresti",  # Row 2  
        "SUN0292 Factura 2024-003 17.07.2024 300.00 RON Bucuresti",  # Row 3
        "SUN0290 Factura 2024-004 18.07.2024 400.00 RON Bucuresti"   # Row 4
    ]
    
    print("Test Records:")
    for i, record in enumerate(test_records):
        print(f"  Record {i+1}: {record.client_id} ({record.invoice_id})")
    
    print("\nMock Table Rows:")
    for i, row_text in enumerate(mock_table_rows):
        print(f"  Row {i+1}: {row_text}")
    
    print("\n" + "=" * 60)
    print("Testing Matching Logic:")
    
    # Test the matching logic for each record
    for record_idx, record in enumerate(test_records):
        print(f"\n🎯 Testing Record {record_idx + 1}: {record.client_id} ({record.invoice_id})")
        
        target_row_index = -1
        match_reason = ""
        
        # Simulate the enhanced matching logic from the automation
        for i, row_text in enumerate(mock_table_rows):
            row_match = False
            
            # Method 1: Check for client ID match
            if record.client_id and record.client_id.strip():
                if record.client_id in row_text:
                    print(f"  ✓ Match found by client ID: {record.client_id}")
                    row_match = True
                    match_reason = f"client_id:{record.client_id}"
            
            # Method 2: For generic invoice IDs, use position-based matching
            if not row_match and record.invoice_id and record.invoice_id.startswith('invoice_'):
                try:
                    # Extract the number from "invoice_X"
                    invoice_num = int(record.invoice_id.split('_')[1])
                    # Use 1-based indexing (invoice_1 = row 0, invoice_2 = row 1, etc.)
                    if i == (invoice_num - 1):
                        print(f"  ✓ Match found by position: {record.invoice_id} -> row {i+1}")
                        row_match = True
                        match_reason = f"position:{record.invoice_id}->row{i+1}"
                except (ValueError, IndexError):
                    print(f"  Could not parse invoice number from: {record.invoice_id}")
            
            if row_match:
                target_row_index = i
                print(f"  SUCCESS: Found target invoice in row {i+1} (reason: {match_reason})")
                break
        
        # Verify the match is correct
        expected_row = record_idx  # Should match the record index (0-based)
        if target_row_index == expected_row:
            print(f"  ✅ CORRECT: Record {record_idx + 1} matched to Row {target_row_index + 1}")
        else:
            print(f"  ❌ ERROR: Record {record_idx + 1} should match Row {expected_row + 1}, but matched Row {target_row_index + 1}")
    
    print("\n" + "=" * 60)
    print("Sequential Processing Test:")
    
    # Test loop prevention logic
    processed_invoices = set()
    
    for record_idx, record in enumerate(test_records):
        invoice_key = f"{record.client_id}_{record.invoice_id}"
        
        print(f"\n📋 Processing Record {record_idx + 1}: {invoice_key}")
        
        # Loop prevention check
        if invoice_key in processed_invoices:
            print(f"  ⚠️ LOOP DETECTED: Invoice {invoice_key} already processed - would skip")
            continue
        
        # Mark as processed
        processed_invoices.add(invoice_key)
        print(f"  ✓ Marked invoice {invoice_key} as being processed")
        print(f"  ✓ Would process: {record.client_id} in row {record_idx + 1}")
    
    print(f"\n📊 Final State:")
    print(f"  Processed invoices: {len(processed_invoices)}")
    print(f"  Expected: {len(test_records)}")
    
    if len(processed_invoices) == len(test_records):
        print(f"  ✅ SUCCESS: All invoices would be processed sequentially")
    else:
        print(f"  ❌ ERROR: Sequential processing failed")
    
    print("\n" + "=" * 60)
    print("Test completed!")


if __name__ == "__main__":
    test_invoice_matching_logic()
