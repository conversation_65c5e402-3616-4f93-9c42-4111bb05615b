#!/usr/bin/env python3
"""
Test the specific address that failed in the automation.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config
from src.geocoding_service import GeocodingService


async def test_specific_address():
    """Test the specific address that failed in the automation."""
    
    # Load configuration
    try:
        config = Config.from_env()
        geocoding = GeocodingService(config)
    except Exception as e:
        print(f"Error loading configuration: {e}")
        print("Make sure you have a .env file with GOOGLE_MAPS_API_KEY")
        return
    
    # The specific address from the automation that didn't detect sector
    address = "Matei Basarab,nr.100, bl.85 ,SC.2, et.4 Ap.47"
    city = "Bucuresti"
    county = "Bucuresti"
    
    print("🔍 Testing Specific Address from Automation")
    print("=" * 60)
    print(f"Address: {address}")
    print(f"City: {city}")
    print(f"County: {county}")
    print()
    
    try:
        # Geocode the address
        new_address, new_city, new_county = await geocoding.geocode_address(address, city, county)
        
        if new_address or new_city or new_county:
            print(f"✅ Original: {address}, {city}, {county}")
            print(f"🎯 Result:   {new_address}, {new_city}, {new_county}")
            
            if "Sector" in new_city:
                print(f"🏙️  SUCCESS: Sector detected - {new_city}")
            else:
                print(f"⚠️  NO SECTOR: City returned as '{new_city}'")
        else:
            print("❌ No geocoding result returned")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")


if __name__ == "__main__":
    asyncio.run(test_specific_address())
