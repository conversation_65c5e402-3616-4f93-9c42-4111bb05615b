```markdown
# Automate Sector Extraction in Client Form

You are tasked with writing a JavaScript module that, when the user clicks **Salveaza date client**, will:

1. **Read the current value of the county field**:
   ```js
   const county = document.getElementById('client_county').value.trim();
   ```

2. **If** `county === '<PERSON><PERSON>uresti'`, **proceed**; otherwise, do nothing.

3. **Read the full client address** from the textarea:
   ```js
   const address = document.getElementById('client_address').value.trim();
   ```

4. **Construct a Google Maps Geocoding API request** to determine the sector. Use your API key, and query with both the county and address:
   ```js
   const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address + ', Bucuresti, Romania')}&key=YOUR_API_KEY`;
   ```

5. **Parse the JSON response** to find the component with types `sublocality_level_1` or `administrative_area_level_2`—that’s the sector:
   ```js
   const sectorComponent = data.results[0]?.address_components.find(c =>
     c.types.includes('sublocality_level_1') ||
     c.types.includes('administrative_area_level_2')
   );
   const sector = sectorComponent ? sectorComponent.long_name : '';
   ```

6. **Replace the `client_city` field** if a sector was found:
   ```js
   if (sector) {
     document.getElementById('client_city').value = sector;
   }
   ```

7. **Ensure your code**:
   - Runs asynchronously (use `async/await` or `.then()`).
   - Handles API errors and missing data gracefully.
   - Does not block the normal form submission if the county isn’t Bucuresti.

8. **Attach** this logic to the **save button** click handler:
   ```js
   document.getElementById('addClientBtn').addEventListener('click', async (e) => {
     const county = document.getElementById('client_county').value.trim();
     if (county !== 'Bucuresti') return;

     const address = document.getElementById('client_address').value.trim();
     const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address + ', Bucuresti, Romania')}&key=YOUR_API_KEY`;

     try {
       const response = await fetch(url);
       const data = await response.json();
       const comp = data.results[0]?.address_components.find(c =>
         c.types.includes('sublocality_level_1') ||
         c.types.includes('administrative_area_level_2')
       );
       if (comp) {
         document.getElementById('client_city').value = comp.long_name;
       }
     } catch (err) {
       console.error('Geocoding failed:', err);
     }

     // proceed with the rest of your save logic…
   });
   ```

This file provides a complete, step-by-step blueprint for a coding agent to implement the requested feature.

