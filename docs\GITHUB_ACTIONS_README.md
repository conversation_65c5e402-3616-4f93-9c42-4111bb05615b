# 🤖 SmartBill Automation - GitHub Actions

This document provides comprehensive information about running the SmartBill automation system on GitHub Actions.

## 🎯 Overview

The GitHub Actions workflow automates the SmartBill address correction process with:
- **Scheduled execution** (daily/weekly)
- **Manual triggers** with configurable parameters
- **Multi-environment support** (production/staging/development)
- **Comprehensive error handling** and notifications
- **Secure credential management**
- **Detailed logging and reporting**

## 🚀 Quick Start

### 1. **Repository Setup**
```bash
# Clone the repository
git clone https://github.com/your-username/auto-smartbill.git
cd auto-smartbill

# Validate setup
python scripts/setup-github-actions.py validate
```

### 2. **Configure Secrets**
Go to: `Repository → Settings → Secrets and variables → Actions`

**Required Secrets:**
- `SMARTBILL_USERNAME` - Your SmartBill login
- `SMARTBILL_PASSWORD` - Your SmartBill password  
- `GOOGLE_MAPS_API_KEY` - Google Maps API key
- `GOOGLE_SHEETS_CREDENTIALS` - Service account JSON
- `GOOGLE_SHEETS_ID` - Target spreadsheet ID

### 3. **Manual Execution**
1. Go to `Actions` tab in GitHub
2. Select "SmartBill Automation System"
3. Click "Run workflow"
4. Configure parameters and execute

## 📋 Workflow Features

### **Trigger Options**
| Trigger | Description | Configuration |
|---------|-------------|---------------|
| Manual | `workflow_dispatch` | Configurable parameters |
| Scheduled | `cron` | Daily at 9 AM UTC |
| Event | `push/PR` | On code changes |

### **Environment Support**
| Environment | Purpose | Max Records | Notifications |
|-------------|---------|-------------|---------------|
| Production | Live processing | 50 | Errors only |
| Staging | Pre-production testing | 10 | All events |
| Development | Testing/debugging | 5 | All events |

### **Configuration Parameters**
- **Environment**: production/staging/development
- **Dry Run**: Test mode without changes
- **Max Records**: Limit processing volume
- **Headless**: Browser visibility mode
- **Notification Level**: Control alert frequency

## 🔧 Advanced Configuration

### **Custom Schedules**
```yaml
# Daily at specific time
schedule:
  - cron: '0 9 * * *'  # 9:00 AM UTC

# Weekdays only
schedule:
  - cron: '0 8 * * 1-5'  # 8:00 AM UTC, Mon-Fri

# Multiple schedules
schedule:
  - cron: '0 9 * * *'   # Daily
  - cron: '0 6 * * 1'   # Weekly on Monday
```

### **Environment Variables**
```yaml
env:
  ENVIRONMENT: production
  DRY_RUN: false
  MAX_RECORDS: 10
  HEADLESS: true
  BROWSER_TIMEOUT: 30000
  MAX_RETRIES: 5
```

### **Notification Configuration**
```yaml
# Telegram notifications
TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}

# Custom webhook
WEBHOOK_URL: ${{ secrets.WEBHOOK_URL }}
```

## 📊 Monitoring and Logging

### **Execution Logs**
- Real-time workflow logs in GitHub Actions
- Detailed step-by-step execution
- Error messages and stack traces
- Performance metrics

### **Artifacts**
- **Logs**: Complete automation logs
- **Screenshots**: Browser screenshots on errors
- **Reports**: Execution summaries
- **Configuration**: Sanitized config files

### **Notifications**
- **Telegram**: Instant failure/success alerts
- **Webhook**: Custom integration support
- **GitHub**: Step summaries and reports

## 🛡️ Security

### **Credential Management**
- All secrets encrypted in GitHub
- No credentials in code or logs
- Automatic cleanup after execution
- Environment-specific isolation

### **Access Control**
- Repository-level permissions
- Branch protection rules
- Required reviews for changes
- Audit trail for all executions

### **Data Protection**
- Secure API communication
- Encrypted data transmission
- Temporary file cleanup
- No sensitive data in artifacts

## 🔍 Troubleshooting

### **Common Issues**

#### **Authentication Failures**
```
❌ Error: Invalid SmartBill credentials
```
**Solution**: Verify `SMARTBILL_USERNAME` and `SMARTBILL_PASSWORD` secrets

#### **Browser Timeout**
```
❌ Error: Navigation timeout exceeded
```
**Solution**: Increase `BROWSER_TIMEOUT` or check network connectivity

#### **API Quota Exceeded**
```
❌ Error: Google Maps API quota exceeded
```
**Solution**: Check API usage in Google Cloud Console

#### **Google Sheets Access**
```
❌ Error: Permission denied for Google Sheets
```
**Solution**: Share sheet with service account email

### **Debugging Steps**
1. **Check workflow logs** in GitHub Actions
2. **Download artifacts** for detailed analysis
3. **Verify secrets** configuration
4. **Test locally** with same parameters
5. **Check API quotas** and limits

### **Performance Issues**
- **Slow execution**: Increase timeout values
- **Memory errors**: Reduce `MAX_RECORDS`
- **Network timeouts**: Check connectivity
- **Browser crashes**: Update Playwright version

## 📈 Performance Optimization

### **Resource Management**
```yaml
# Optimize for performance
timeout-minutes: 90
strategy:
  fail-fast: false
  max-parallel: 1
```

### **Caching**
```yaml
# Cache dependencies
- uses: actions/setup-python@v4
  with:
    cache: 'pip'
```

### **Concurrency Control**
```yaml
# Prevent overlapping runs
concurrency:
  group: smartbill-automation
  cancel-in-progress: true
```

## 🔄 Maintenance

### **Regular Tasks**
- **Weekly**: Review execution logs
- **Monthly**: Update dependencies
- **Quarterly**: Rotate secrets
- **Annually**: Review configuration

### **Updates**
```bash
# Update dependencies
pip install --upgrade -r requirements.txt

# Update Playwright
playwright install chromium

# Test changes
python scripts/setup-github-actions.py validate
```

### **Monitoring**
- Set up alerts for failures
- Track execution metrics
- Monitor API usage
- Review performance trends

## 📞 Support

### **Documentation**
- [GitHub Actions Deployment Guide](../GITHUB_ACTIONS_DEPLOYMENT_GUIDE.md)
- [Configuration Reference](../config/github-actions-config.yml)
- [Setup Script](../scripts/setup-github-actions.py)

### **Getting Help**
1. Check workflow logs and artifacts
2. Review troubleshooting section
3. Test components locally
4. Verify configuration and secrets
5. Check API status pages

### **Best Practices**
- Test in staging before production
- Use dry-run mode for validation
- Monitor execution regularly
- Keep dependencies updated
- Document configuration changes

---

## ✅ Status Dashboard

| Component | Status | Last Updated |
|-----------|--------|--------------|
| Workflow | ✅ Active | 2024-01-XX |
| Dependencies | ✅ Current | 2024-01-XX |
| Secrets | ✅ Valid | 2024-01-XX |
| Monitoring | ✅ Active | 2024-01-XX |

**Next Scheduled Run**: Check Actions tab for upcoming executions

**Recent Performance**: View artifacts and logs for latest metrics

---

*For technical support or questions, please check the documentation or create an issue in the repository.*
