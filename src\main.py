"""Main workflow orchestrator for SmartBill address correction automation."""

import asyncio
import logging
import sys
import time
from typing import List, Tuple

from .config import Config
from .smartbill_automation import SmartBillAutomation, AddressRecord
from .geocoding_service import GeocodingService
from .sheets_logger import Sheets<PERSON>ogger
from .telegram_notifier import TelegramNotifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('smartbill_automation.log')
    ]
)

logger = logging.getLogger(__name__)


class WorkflowOrchestrator:
    """Orchestrates the entire SmartBill address correction workflow."""
    
    def __init__(self, config: Config):
        self.config = config
        self.smartbill = SmartBillAutomation(config)
        self.geocoding = GeocodingService(config)
        self.sheets_logger = SheetsLogger(config)

        # Initialize Telegram notifier only if credentials are available
        if config.has_telegram_config():
            try:
                self.notifier = TelegramNotifier(config)
                self.has_telegram = True
                logger.info("Telegram notifications enabled")
            except ImportError as e:
                self.notifier = None
                self.has_telegram = False
                logger.warning(f"Telegram library not available - notifications will be skipped: {e}")
            except Exception as e:
                self.notifier = None
                self.has_telegram = False
                logger.warning(f"Failed to initialize Telegram - notifications will be skipped: {e}")
        else:
            self.notifier = None
            self.has_telegram = False
            logger.warning("Telegram credentials not provided - notifications will be skipped")
        
        # Statistics
        self.total_records = 0
        self.successful_corrections = 0
        self.failed_corrections = 0
        self.skipped_records = 0
        self.errors: List[str] = []

    async def safe_notify(self, method_name: str, *args, **kwargs) -> bool:
        """
        Safely send a notification, skipping if Telegram is not configured.

        Args:
            method_name: Name of the notification method to call
            *args: Arguments to pass to the method
            **kwargs: Keyword arguments to pass to the method

        Returns:
            bool: True if notification sent or skipped, False if error
        """
        if not self.has_telegram:
            logger.info(f"Skipping Telegram notification: {method_name}")
            return True

        try:
            method = getattr(self.notifier, method_name)
            return await method(*args, **kwargs)
        except Exception as e:
            logger.error(f"Failed to send Telegram notification {method_name}: {e}")
            return False
    
    async def retry_with_backoff(self, func, *args, **kwargs):
        """
        Execute a function with exponential backoff retry logic.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result or None if all retries failed
        """
        for attempt in range(1, self.config.max_retries + 1):
            try:
                result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
                return result
            except Exception as e:
                error_msg = f"Attempt {attempt}/{self.config.max_retries} failed: {str(e)}"
                logger.warning(error_msg)
                
                if attempt < self.config.max_retries:
                    # Send retry notification for critical failures
                    if attempt > 2:  # Only notify after multiple failures
                        await self.safe_notify('notify_retry', attempt, self.config.max_retries, str(e))
                    
                    # Exponential backoff
                    delay = self.config.retry_delay * (2 ** (attempt - 1))
                    logger.info(f"Waiting {delay} seconds before retry...")
                    await asyncio.sleep(delay)
                else:
                    self.errors.append(error_msg)
                    logger.error(f"All retry attempts failed for function {func.__name__}")
                    return None
    
    async def process_single_record(self, record: AddressRecord) -> Tuple[str, str]:
        """
        Process a single address record by opening the edit page and updating the address.

        Args:
            record: AddressRecord to process

        Returns:
            Tuple[str, str]: (status, error_message)
        """
        try:
            logger.info(f"Processing record for client {record.client_id}")

            # The new approach: update_invoice_address will handle everything:
            # 1. Open edit page in new tab
            # 2. Extract current address from edit form
            # 3. Geocode the address
            # 4. Update the form with new address
            # 5. Save and close
            update_success = await self.retry_with_backoff(
                self.smartbill.update_invoice_address,
                record
            )

            if update_success:
                logger.info(f"Successfully processed invoice for client {record.client_id}")
                return "SUCCESS", ""
            else:
                error_msg = f"Failed to process invoice for client {record.client_id}"
                logger.error(error_msg)
                return "FAILED", error_msg
                
        except Exception as e:
            error_msg = f"Unexpected error processing client {record.client_id}: {str(e)}"
            logger.error(error_msg)
            return "FAILED", error_msg
    
    async def run_workflow(self) -> bool:
        """
        Run the complete address correction workflow.
        
        Returns:
            bool: True if workflow completed successfully, False otherwise
        """
        try:
            logger.info("Starting SmartBill address correction workflow")
            
            # Send start notification
            await self.safe_notify('notify_start')
            
            # Initialize SmartBill automation
            async with self.smartbill:
                # Login to SmartBill
                login_success = await self.retry_with_backoff(self.smartbill.login)
                if not login_success:
                    raise Exception("Failed to login to SmartBill after all retries")
                
                # Navigate to e-Invoice errors
                nav_success = await self.retry_with_backoff(self.smartbill.navigate_to_einvoice_errors)
                if not nav_success:
                    raise Exception("Failed to navigate to e-Invoice errors after all retries")

                # Fetch records needing correction
                records = await self.retry_with_backoff(self.smartbill.fetch_einvoice_errors)
                if records is None:
                    raise Exception("Failed to fetch e-Invoice error records after all retries")
                
                self.total_records = len(records)
                
                if self.total_records == 0:
                    logger.info("No e-Invoice error records found")
                    await self.safe_notify('notify_no_records')
                    return True
                
                logger.info(f"Found {self.total_records} records to process")

                # Process each record (address fetching and updating combined)
                log_entries = []

                for i, record in enumerate(records, 1):
                    logger.info(f"=== PROCESSING RECORD {i}/{self.total_records} ===")
                    logger.info(f"Client: {record.client_id}")
                    logger.info(f"Invoice ID: {record.invoice_id}")
                    logger.info(f"Progress: {i}/{self.total_records} ({(i/self.total_records)*100:.1f}%)")

                    status, error_message = await self.process_single_record(record)

                    logger.info(f"=== RECORD {i}/{self.total_records} COMPLETED ===")
                    logger.info(f"Status: {status}")
                    if error_message:
                        logger.info(f"Error: {error_message}")
                    
                    # Update statistics
                    if status == "SUCCESS":
                        self.successful_corrections += 1
                    elif status == "FAILED":
                        self.failed_corrections += 1
                    elif status == "SKIPPED":
                        self.skipped_records += 1
                    
                    # Prepare log entry
                    log_entries.append((record, status, error_message))
                    
                    # Log to sheets immediately for each record (in case of failure)
                    self.sheets_logger.log_correction(record, status, error_message)
                
                # Log batch summary
                self.sheets_logger.log_run_summary(
                    self.total_records,
                    self.successful_corrections,
                    self.failed_corrections,
                    self.skipped_records,
                    self.errors
                )
                
                # Send appropriate notification
                sheets_url = self.sheets_logger.get_spreadsheet_url()
                
                if self.failed_corrections == 0:
                    # Complete success
                    await self.safe_notify('notify_success',
                        self.total_records,
                        self.successful_corrections,
                        self.failed_corrections,
                        self.skipped_records,
                        sheets_url
                    )
                else:
                    # Partial success
                    await self.safe_notify('notify_partial_success',
                        self.successful_corrections,
                        self.failed_corrections,
                        self.errors,
                        sheets_url
                    )
                
                logger.info(f"Workflow completed: {self.successful_corrections} successful, {self.failed_corrections} failed, {self.skipped_records} skipped")
                return True
                
        except Exception as e:
            error_msg = f"Workflow failed: {str(e)}"
            logger.error(error_msg)
            
            # Send failure notification
            sheets_url = self.sheets_logger.get_spreadsheet_url()
            await self.safe_notify('notify_failure',
                error_msg,
                self.total_records,
                self.successful_corrections + self.failed_corrections + self.skipped_records,
                sheets_url
            )
            
            return False


async def main():
    """Main entry point for the application."""
    try:
        # Load configuration
        config = Config.from_env()
        config.validate()
        
        # Create and run workflow
        orchestrator = WorkflowOrchestrator(config)
        success = await orchestrator.run_workflow()
        
        # Exit with appropriate code
        sys.exit(0 if success else 1)
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
