# GitHub Actions Configuration for SmartBill Automation
# This file defines environment-specific settings for different deployment scenarios

environments:
  production:
    description: "Production environment for live SmartBill processing"
    settings:
      max_records: 50
      headless: true
      browser_timeout: 45000
      retry_attempts: 5
      retry_delay: 2.0
      notification_level: "errors_only"
      dry_run: false
    schedule:
      # Daily at 9:00 AM UTC (11:00 AM Romania time)
      daily: "0 9 * * *"
      # Weekly on Monday at 6:00 AM UTC
      weekly: "0 6 * * 1"
    notifications:
      telegram: true
      webhook: true
      github_summary: true
    
  staging:
    description: "Staging environment for testing before production"
    settings:
      max_records: 10
      headless: true
      browser_timeout: 30000
      retry_attempts: 3
      retry_delay: 1.0
      notification_level: "all"
      dry_run: false
    schedule:
      # Daily at 8:00 AM UTC for testing
      daily: "0 8 * * *"
    notifications:
      telegram: true
      webhook: false
      github_summary: true
    
  development:
    description: "Development environment for testing and debugging"
    settings:
      max_records: 5
      headless: false
      browser_timeout: 60000
      retry_attempts: 2
      retry_delay: 0.5
      notification_level: "all"
      dry_run: true
    schedule:
      # Manual trigger only
      manual_only: true
    notifications:
      telegram: false
      webhook: false
      github_summary: true

# Workflow trigger configurations
triggers:
  manual:
    description: "Manual workflow dispatch with configurable parameters"
    inputs:
      environment:
        type: "choice"
        options: ["production", "staging", "development"]
        default: "production"
      dry_run:
        type: "boolean"
        default: false
      max_records:
        type: "string"
        default: "10"
      headless:
        type: "choice"
        options: ["true", "false"]
        default: "true"
      notification_level:
        type: "choice"
        options: ["all", "errors_only", "none"]
        default: "errors_only"
  
  scheduled:
    production:
      # Daily execution
      - cron: "0 9 * * *"
        description: "Daily at 9:00 AM UTC"
      # Weekly execution
      - cron: "0 6 * * 1"
        description: "Weekly on Monday at 6:00 AM UTC"
    
    staging:
      # Daily execution for testing
      - cron: "0 8 * * *"
        description: "Daily at 8:00 AM UTC (staging)"
  
  events:
    push:
      branches: ["main", "develop"]
      paths: ["src/**", "requirements.txt", ".github/workflows/**"]
    pull_request:
      branches: ["main"]
      paths: ["src/**", "requirements.txt"]

# Required secrets configuration
secrets:
  required:
    - name: "SMARTBILL_USERNAME"
      description: "SmartBill login username"
      type: "string"
    - name: "SMARTBILL_PASSWORD"
      description: "SmartBill login password"
      type: "string"
    - name: "GOOGLE_MAPS_API_KEY"
      description: "Google Maps API key for geocoding"
      type: "string"
    - name: "GOOGLE_SHEETS_CREDENTIALS"
      description: "Google Sheets service account JSON credentials"
      type: "json"
    - name: "GOOGLE_SHEETS_ID"
      description: "Target Google Sheets document ID"
      type: "string"
  
  optional:
    - name: "SMARTBILL_MFA_SECRET"
      description: "TOTP secret for SmartBill 2FA"
      type: "string"
    - name: "TELEGRAM_BOT_TOKEN"
      description: "Telegram bot token for notifications"
      type: "string"
    - name: "TELEGRAM_CHAT_ID"
      description: "Telegram chat ID for notifications"
      type: "string"
    - name: "WEBHOOK_URL"
      description: "Custom webhook URL for notifications"
      type: "url"

# Browser and system configuration
browser:
  type: "chromium"
  headless: true
  timeout: 30000
  viewport:
    width: 1920
    height: 1080
  user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  
system:
  python_version: "3.11"
  node_version: "18"
  ubuntu_version: "latest"
  timeout_minutes: 90
  
# Artifact configuration
artifacts:
  logs:
    retention_days: 30
    include:
      - "automation.log"
      - "logs/"
      - "screenshots/"
      - "*.log"
  
  reports:
    retention_days: 90
    include:
      - "reports/"
      - "summaries/"

# Notification templates
notifications:
  telegram:
    success: |
      ✅ SmartBill Automation Completed Successfully
      
      Environment: {environment}
      Run ID: {run_id}
      Records Processed: {records_processed}
      Duration: {duration}
      Triggered by: {actor}
    
    failure: |
      ❌ SmartBill Automation Failed
      
      Environment: {environment}
      Run ID: {run_id}
      Error: {error_message}
      Triggered by: {actor}
      
      Check GitHub Actions for details.
  
  webhook:
    success:
      color: "good"
      title: "SmartBill Automation Success"
      fields:
        - title: "Environment"
          value: "{environment}"
          short: true
        - title: "Run ID"
          value: "{run_id}"
          short: true
        - title: "Records"
          value: "{records_processed}"
          short: true
        - title: "Duration"
          value: "{duration}"
          short: true
    
    failure:
      color: "danger"
      title: "SmartBill Automation Failure"
      fields:
        - title: "Environment"
          value: "{environment}"
          short: true
        - title: "Run ID"
          value: "{run_id}"
          short: true
        - title: "Error"
          value: "{error_message}"
          short: false

# Security and compliance
security:
  secrets_scanning: true
  dependency_review: true
  code_scanning: true
  
compliance:
  data_retention: "30 days"
  logging_level: "INFO"
  audit_trail: true

# Performance optimization
performance:
  caching:
    pip: true
    playwright: true
  
  concurrency:
    max_parallel_jobs: 1
    cancel_in_progress: true
  
  resource_limits:
    memory: "4GB"
    cpu: "2 cores"
    disk: "14GB"

# Monitoring and alerting
monitoring:
  health_checks:
    - name: "Workflow execution time"
      threshold: "60 minutes"
      action: "alert"
    
    - name: "Success rate"
      threshold: "90%"
      period: "7 days"
      action: "alert"
    
    - name: "API quota usage"
      threshold: "80%"
      action: "warn"
  
  metrics:
    - execution_time
    - success_rate
    - records_processed
    - error_rate
    - api_usage

# Backup and recovery
backup:
  configuration:
    frequency: "weekly"
    retention: "3 months"
  
  data:
    google_sheets: "automatic"
    logs: "30 days"
  
recovery:
  rollback_strategy: "previous_version"
  emergency_contacts:
    - "<EMAIL>"
  
  procedures:
    - "Stop automation"
    - "Investigate issue"
    - "Apply fix"
    - "Test in staging"
    - "Deploy to production"
