name: SmartBill Automation System

on:
  # Manual trigger with comprehensive options
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to run against'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
          - development
      dry_run:
        description: 'Run in dry-run mode (no actual changes)'
        required: false
        default: false
        type: boolean
      max_records:
        description: 'Maximum number of records to process'
        required: false
        default: '10'
        type: string
      headless:
        description: 'Run browser in headless mode'
        required: false
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'
      notification_level:
        description: 'Notification level'
        required: false
        default: 'errors_only'
        type: choice
        options:
          - 'all'
          - 'errors_only'
          - 'none'

  # Scheduled execution options
  schedule:
    # Daily at 9:00 AM UTC (11:00 AM Romania time)
    - cron: '0 9 * * *'
    # Weekly on Monday at 6:00 AM UTC (8:00 AM Romania time)
    - cron: '0 6 * * 1'

  # Event-triggered execution
  push:
    branches:
      - main
      - develop
    paths:
      - 'src/**'
      - 'requirements.txt'
      - '.github/workflows/smartbill-automation.yml'

  pull_request:
    branches:
      - main
    paths:
      - 'src/**'
      - 'requirements.txt'

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  smartbill-automation:
    runs-on: ubuntu-latest
    timeout-minutes: 90

    strategy:
      fail-fast: false
      matrix:
        environment: [production]

    environment: ${{ matrix.environment }}

    steps:
      - name: 🔄 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'

      - name: 📦 Install Python Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: 🌐 Install Playwright Browsers
        run: |
          playwright install chromium
          playwright install-deps chromium

      - name: 🔧 Configure Environment
        run: |
          # Create necessary directories
          mkdir -p logs
          mkdir -p data
          mkdir -p screenshots

          # Set up environment variables
          echo "ENVIRONMENT=${{ github.event.inputs.environment || 'production' }}" >> $GITHUB_ENV
          echo "DRY_RUN=${{ github.event.inputs.dry_run || 'false' }}" >> $GITHUB_ENV
          echo "MAX_RECORDS=${{ github.event.inputs.max_records || '10' }}" >> $GITHUB_ENV
          echo "HEADLESS=${{ github.event.inputs.headless || 'true' }}" >> $GITHUB_ENV
          echo "NOTIFICATION_LEVEL=${{ github.event.inputs.notification_level || 'errors_only' }}" >> $GITHUB_ENV
          echo "GITHUB_RUN_ID=${{ github.run_id }}" >> $GITHUB_ENV
          echo "GITHUB_RUN_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV
          echo "GITHUB_ACTOR=${{ github.actor }}" >> $GITHUB_ENV
          echo "GITHUB_REPOSITORY=${{ github.repository }}" >> $GITHUB_ENV
          echo "GITHUB_REF_NAME=${{ github.ref_name }}" >> $GITHUB_ENV

      - name: 🔐 Set up Google Sheets Credentials
        run: |
          echo '${{ secrets.GOOGLE_SHEETS_CREDENTIALS }}' > google_sheets_credentials.json
          echo "GOOGLE_APPLICATION_CREDENTIALS=google_sheets_credentials.json" >> $GITHUB_ENV

      - name: ✅ Validate Configuration
        run: |
          python -c "
          import os
          import json

          # Check required secrets
          required_secrets = [
              'SMARTBILL_USERNAME',
              'SMARTBILL_PASSWORD',
              'GOOGLE_MAPS_API_KEY',
              'GOOGLE_SHEETS_CREDENTIALS'
          ]

          missing_secrets = []
          for secret in required_secrets:
              if secret == 'GOOGLE_SHEETS_CREDENTIALS':
                  if not os.path.exists('google_sheets_credentials.json'):
                      missing_secrets.append(secret)
              elif not os.environ.get(secret):
                  missing_secrets.append(secret)

          if missing_secrets:
              print(f'❌ Missing required secrets: {missing_secrets}')
              exit(1)
          else:
              print('✅ All required secrets are configured')

          # Validate Google Sheets credentials format
          try:
              with open('google_sheets_credentials.json', 'r') as f:
                  creds = json.load(f)
              if 'type' not in creds or 'project_id' not in creds:
                  print('❌ Invalid Google Sheets credentials format')
                  exit(1)
              print('✅ Google Sheets credentials format is valid')
          except Exception as e:
              print(f'❌ Error validating Google Sheets credentials: {e}')
              exit(1)
          "
        env:
          SMARTBILL_USERNAME: ${{ secrets.SMARTBILL_USERNAME }}
          SMARTBILL_PASSWORD: ${{ secrets.SMARTBILL_PASSWORD }}
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}

      - name: 🚀 Run SmartBill Automation
        id: automation
        run: |
          echo "🚀 Starting SmartBill Automation..."
          echo "Environment: $ENVIRONMENT"
          echo "Dry Run: $DRY_RUN"
          echo "Max Records: $MAX_RECORDS"
          echo "Headless: $HEADLESS"
          echo "GitHub Run ID: $GITHUB_RUN_ID"

          # Run the automation with error handling
          python run_local.py \
            --environment "$ENVIRONMENT" \
            --dry-run "$DRY_RUN" \
            --max-records "$MAX_RECORDS" \
            --headless "$HEADLESS" \
            --github-run-id "$GITHUB_RUN_ID" \
            2>&1 | tee automation.log

          # Capture exit code
          exit_code=${PIPESTATUS[0]}
          echo "automation_exit_code=$exit_code" >> $GITHUB_OUTPUT

          if [ $exit_code -eq 0 ]; then
            echo "✅ Automation completed successfully"
          else
            echo "❌ Automation failed with exit code $exit_code"
          fi
        env:
          # SmartBill credentials
          SMARTBILL_USERNAME: ${{ secrets.SMARTBILL_USERNAME }}
          SMARTBILL_PASSWORD: ${{ secrets.SMARTBILL_PASSWORD }}
          SMARTBILL_MFA_SECRET: ${{ secrets.SMARTBILL_MFA_SECRET }}

          # Google APIs
          GOOGLE_MAPS_API_KEY: ${{ secrets.GOOGLE_MAPS_API_KEY }}
          GOOGLE_SHEETS_ID: ${{ secrets.GOOGLE_SHEETS_ID }}

          # Telegram notifications
          TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}

          # Browser settings
          BROWSER_TIMEOUT: 30000

          # Retry settings
          MAX_RETRIES: 5
          RETRY_DELAY: 1.0

          # Timezone
          TIMEZONE: Europe/Bucharest

      - name: 📊 Generate Summary Report
        if: always()
        run: |
          echo "## 📊 SmartBill Automation Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** $ENVIRONMENT" >> $GITHUB_STEP_SUMMARY
          echo "**Dry Run:** $DRY_RUN" >> $GITHUB_STEP_SUMMARY
          echo "**Max Records:** $MAX_RECORDS" >> $GITHUB_STEP_SUMMARY
          echo "**Headless:** $HEADLESS" >> $GITHUB_STEP_SUMMARY
          echo "**Run ID:** $GITHUB_RUN_ID" >> $GITHUB_STEP_SUMMARY
          echo "**Triggered by:** $GITHUB_ACTOR" >> $GITHUB_STEP_SUMMARY
          echo "**Exit Code:** ${{ steps.automation.outputs.automation_exit_code }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ "${{ steps.automation.outputs.automation_exit_code }}" = "0" ]; then
            echo "**Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY
          else
            echo "**Status:** ❌ Failed" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📝 Execution Log (Last 50 lines)" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          if [ -f automation.log ]; then
            tail -n 50 automation.log >> $GITHUB_STEP_SUMMARY
          else
            echo "No automation log found" >> $GITHUB_STEP_SUMMARY
          fi
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY

      - name: 📤 Upload Logs and Artifacts
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: smartbill-automation-logs-${{ github.run_id }}
          path: |
            automation.log
            logs/
            screenshots/
            *.log
          retention-days: 30

      - name: 🧹 Cleanup Sensitive Files
        if: always()
        run: |
          rm -f google_sheets_credentials.json
          rm -f credentials.json
          rm -f *.log

      - name: 📧 Notify on Failure
        if: failure() && (github.event.inputs.notification_level != 'none')
        run: |
          echo "❌ SmartBill Automation failed!"
          echo "Run ID: $GITHUB_RUN_ID"
          echo "Environment: $ENVIRONMENT"
          echo "Triggered by: $GITHUB_ACTOR"
          echo "Repository: $GITHUB_REPOSITORY"
          echo "Branch: $GITHUB_REF_NAME"

          # Send Telegram notification if configured
          if [ -n "${{ secrets.TELEGRAM_BOT_TOKEN }}" ] && [ -n "${{ secrets.TELEGRAM_CHAT_ID }}" ]; then
            curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
              -d chat_id="${{ secrets.TELEGRAM_CHAT_ID }}" \
              -d text="❌ SmartBill Automation Failed%0A%0AEnvironment: $ENVIRONMENT%0ARun ID: $GITHUB_RUN_ID%0ATriggered by: $GITHUB_ACTOR%0ARepository: $GITHUB_REPOSITORY%0ABranch: $GITHUB_REF_NAME%0A%0ACheck GitHub Actions for details."
          fi

          # Send webhook notification if configured
          if [ -n "${{ secrets.WEBHOOK_URL }}" ]; then
            curl -X POST "${{ secrets.WEBHOOK_URL }}" \
              -H "Content-Type: application/json" \
              -d '{
                "text": "❌ SmartBill Automation Failed",
                "attachments": [{
                  "color": "danger",
                  "fields": [
                    {"title": "Environment", "value": "'$ENVIRONMENT'", "short": true},
                    {"title": "Run ID", "value": "'$GITHUB_RUN_ID'", "short": true},
                    {"title": "Repository", "value": "'$GITHUB_REPOSITORY'", "short": true},
                    {"title": "Branch", "value": "'$GITHUB_REF_NAME'", "short": true},
                    {"title": "Triggered by", "value": "'$GITHUB_ACTOR'", "short": true}
                  ]
                }]
              }'
          fi

      - name: 🎉 Notify on Success
        if: success() && (github.event.inputs.notification_level == 'all')
        run: |
          echo "✅ SmartBill Automation completed successfully!"
          echo "Run ID: $GITHUB_RUN_ID"
          echo "Environment: $ENVIRONMENT"

          # Send Telegram notification if configured
          if [ -n "${{ secrets.TELEGRAM_BOT_TOKEN }}" ] && [ -n "${{ secrets.TELEGRAM_CHAT_ID }}" ]; then
            curl -s -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
              -d chat_id="${{ secrets.TELEGRAM_CHAT_ID }}" \
              -d text="✅ SmartBill Automation Completed Successfully%0A%0AEnvironment: $ENVIRONMENT%0ARun ID: $GITHUB_RUN_ID%0ATriggered by: $GITHUB_ACTOR"
          fi
