#!/usr/bin/env python3
"""
Simple test to check if we can load the SmartBill page.
"""

import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def test_simple_load():
    """Test simple page loading."""
    
    print("Testing simple page load...")
    
    playwright = None
    browser = None
    
    try:
        # Start playwright
        print("Starting Playwright...")
        playwright = await async_playwright().start()
        
        # Launch browser
        print("Launching browser...")
        browser = await playwright.chromium.launch(
            headless=False,  # Visible browser for debugging
            args=[
                '--no-sandbox', 
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # Create context
        print("Creating browser context...")
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        # Create page
        print("Creating page...")
        page = await context.new_page()
        page.set_default_timeout(60000)  # 60 second timeout
        
        # Navigate to SmartBill
        print("Navigating to SmartBill...")
        await page.goto("https://cloud.smartbill.ro/auth/login/", wait_until='domcontentloaded')
        
        print("Page loaded successfully!")
        
        # Get page title
        title = await page.title()
        print(f"Page title: {title}")
        
        # Wait to see the page
        print("Waiting 10 seconds for you to see the page...")
        await asyncio.sleep(10)
        
        # Check for cookie popup
        print("Checking for cookie popup...")
        cookie_button = await page.query_selector('#onetrust-accept-btn-handler')
        if cookie_button:
            is_visible = await cookie_button.is_visible()
            print(f"Cookie button found, visible: {is_visible}")
            if is_visible:
                print("Clicking cookie button...")
                await cookie_button.click()
                print("Cookie button clicked!")
                await asyncio.sleep(2)
        else:
            print("No cookie button found")
        
        # Check for login form
        print("Checking for login form...")
        username_field = await page.query_selector('#id_username')
        password_field = await page.query_selector('#id_password')
        login_button = await page.query_selector('#cloud_login_btn')
        
        print(f"Username field found: {username_field is not None}")
        print(f"Password field found: {password_field is not None}")
        print(f"Login button found: {login_button is not None}")
        
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        try:
            if browser:
                await browser.close()
                print("Browser closed")
        except Exception as e:
            print(f"Error closing browser: {e}")


if __name__ == "__main__":
    asyncio.run(test_simple_load())
