The SmartBill automation has a critical sequential processing issue and needs enhanced debugging. Please fix the following problems:

**Issue 1: Sequential Processing Failure**
- The logs show "processed 7/7 records" but only the first invoice edit page was actually opened
- Subsequent invoices (2-7) are failing with "Target page, context or browser has been closed" errors
- The automation is incorrectly marking failed records as processed
- Fix the page reference management to ensure each invoice is processed sequentially and successfully

**Issue 2: Enhanced Client Edit Debugging Required**
Add comprehensive debugging for the client edit modal interaction process:

1. **Field Extraction Debugging**: Log each extracted field with clear labels:
   - Current address value and which selector found it
   - Current city value and which selector found it  
   - Current county value and which selector found it
   - Log if any fields are empty or not found

2. **Geocoding Process Debugging**: Log the complete geocoding workflow:
   - Original address before cleaning
   - Cleaned address after comma removal
   - Exact Google Maps API query sent
   - Full API response received
   - Parsed results (new address, city, county)
   - Whether a sector was detected or not

3. **Field Update Verification**: After updating any field, verify the change took effect:
   - Log the old value before update
   - Log the new value being set
   - Read back the field value after update to confirm it changed
   - Report success/failure of each field update

**Expected Behavior**: 
- Process all 7 invoices sequentially (one at a time, fully complete each before moving to next)
- Provide detailed logging for every step of client address extraction and updating
- Verify that field updates actually take effect in the UI
- Handle page reference issues properly to prevent "browser closed" errors

**Context**: This is for the SmartBill invoice editing automation that opens edit pages, extracts client addresses, geocodes them to find sectors, and updates the city field with sector information.