"""Google Sheets logging service for recording address corrections."""

import logging
from datetime import datetime
from typing import List, Optional

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from .config import Config
from .smartbill_automation import AddressRecord

logger = logging.getLogger(__name__)


class SheetsLogger:
    """Handles logging of address corrections to Google Sheets."""
    
    def __init__(self, config: Config):
        self.config = config
        self.service = None
        self.spreadsheet_id = config.google_sheets_id
        
    def authenticate(self) -> bool:
        """
        Authenticate with Google Sheets API using service account credentials.
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            # Use service account credentials
            credentials = ServiceAccountCredentials.from_service_account_file(
                self.config.google_sheets_credentials_path,
                scopes=['https://www.googleapis.com/auth/spreadsheets']
            )
            
            self.service = build('sheets', 'v4', credentials=credentials)
            logger.info("Successfully authenticated with Google Sheets API")
            return True
            
        except Exception as e:
            logger.error(f"Failed to authenticate with Google Sheets API: {e}")
            return False
    
    def ensure_headers(self) -> bool:
        """
        Ensure the spreadsheet has proper headers.
        
        Returns:
            bool: True if headers are set up correctly, False otherwise
        """
        try:
            headers = [
                'Timestamp',
                'Client ID',
                'Old Address',
                'Old City', 
                'Old County',
                'New Address',
                'New City',
                'New County',
                'Status',
                'Error Message'
            ]
            
            # Check if headers already exist
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range='A1:J1'
            ).execute()
            
            existing_values = result.get('values', [])
            
            if not existing_values or existing_values[0] != headers:
                # Set headers
                self.service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range='A1:J1',
                    valueInputOption='RAW',
                    body={'values': [headers]}
                ).execute()
                
                logger.info("Headers set up in Google Sheets")
            
            return True
            
        except HttpError as e:
            logger.error(f"Failed to set up headers in Google Sheets: {e}")
            return False
    
    def log_correction(self, record: AddressRecord, status: str, error_message: str = "") -> bool:
        """
        Log a single address correction to Google Sheets.
        
        Args:
            record: AddressRecord with correction information
            status: Status of the correction (SUCCESS, FAILED, SKIPPED)
            error_message: Error message if correction failed
            
        Returns:
            bool: True if logging successful, False otherwise
        """
        try:
            if not self.service:
                if not self.authenticate():
                    return False
            
            # Ensure headers exist
            if not self.ensure_headers():
                return False
            
            # Prepare row data
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            row_data = [
                timestamp,
                record.client_id,
                record.current_address,
                record.current_city,
                record.current_county,
                record.new_address or "",
                record.new_city or "",
                record.new_county or "",
                status,
                error_message
            ]
            
            # Append row to spreadsheet
            self.service.spreadsheets().values().append(
                spreadsheetId=self.spreadsheet_id,
                range='A:J',
                valueInputOption='RAW',
                insertDataOption='INSERT_ROWS',
                body={'values': [row_data]}
            ).execute()
            
            logger.debug(f"Logged correction for client {record.client_id} with status {status}")
            return True
            
        except HttpError as e:
            logger.error(f"Failed to log correction to Google Sheets: {e}")
            return False
    
    def log_batch_corrections(self, records: List[tuple]) -> bool:
        """
        Log multiple address corrections in a single batch operation.
        
        Args:
            records: List of tuples (AddressRecord, status, error_message)
            
        Returns:
            bool: True if logging successful, False otherwise
        """
        try:
            if not self.service:
                if not self.authenticate():
                    return False
            
            # Ensure headers exist
            if not self.ensure_headers():
                return False
            
            # Prepare batch data
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            batch_data = []
            
            for record, status, error_message in records:
                row_data = [
                    timestamp,
                    record.client_id,
                    record.current_address,
                    record.current_city,
                    record.current_county,
                    record.new_address or "",
                    record.new_city or "",
                    record.new_county or "",
                    status,
                    error_message
                ]
                batch_data.append(row_data)
            
            # Append all rows in a single operation
            self.service.spreadsheets().values().append(
                spreadsheetId=self.spreadsheet_id,
                range='A:J',
                valueInputOption='RAW',
                insertDataOption='INSERT_ROWS',
                body={'values': batch_data}
            ).execute()
            
            logger.info(f"Logged {len(batch_data)} corrections to Google Sheets")
            return True
            
        except HttpError as e:
            logger.error(f"Failed to log batch corrections to Google Sheets: {e}")
            return False
    
    def log_run_summary(self, total_records: int, successful: int, failed: int, skipped: int, errors: List[str]) -> bool:
        """
        Log a summary of the entire run.
        
        Args:
            total_records: Total number of records processed
            successful: Number of successful corrections
            failed: Number of failed corrections
            skipped: Number of skipped records
            errors: List of error messages
            
        Returns:
            bool: True if logging successful, False otherwise
        """
        try:
            if not self.service:
                if not self.authenticate():
                    return False
            
            # Create summary record
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            summary_data = [
                timestamp,
                "RUN_SUMMARY",
                f"Total: {total_records}",
                f"Successful: {successful}",
                f"Failed: {failed}",
                f"Skipped: {skipped}",
                "",
                "",
                "SUMMARY",
                "; ".join(errors) if errors else ""
            ]
            
            # Append summary row
            self.service.spreadsheets().values().append(
                spreadsheetId=self.spreadsheet_id,
                range='A:J',
                valueInputOption='RAW',
                insertDataOption='INSERT_ROWS',
                body={'values': [summary_data]}
            ).execute()
            
            logger.info("Logged run summary to Google Sheets")
            return True
            
        except HttpError as e:
            logger.error(f"Failed to log run summary to Google Sheets: {e}")
            return False
    
    def get_spreadsheet_url(self) -> str:
        """
        Get the URL of the Google Sheets spreadsheet.
        
        Returns:
            str: URL of the spreadsheet
        """
        return f"https://docs.google.com/spreadsheets/d/{self.spreadsheet_id}/edit"
