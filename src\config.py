"""Configuration management for SmartBill automation."""

import os
from dataclasses import dataclass
from typing import Optional

from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class Config:
    """Configuration class for SmartBill automation."""
    
    # SmartBill credentials
    smartbill_username: str
    smartbill_password: str
    totp_secret: str
    
    # Google APIs
    google_maps_api_key: str
    google_sheets_credentials_path: str
    google_sheets_id: str
    
    # Telegram
    telegram_bot_token: str
    telegram_chat_id: str
    
    # Browser settings
    headless: bool = True
    browser_timeout: int = 30000  # 30 seconds
    
    # Retry settings
    max_retries: int = 5
    retry_delay: float = 1.0  # Initial delay in seconds
    
    # Timezone
    timezone: str = "Europe/Bucharest"
    
    @classmethod
    def from_env(cls) -> "Config":
        """Create configuration from environment variables."""
        return cls(
            smartbill_username=os.getenv("SMARTBILL_USERNAME", ""),
            smartbill_password=os.getenv("SMARTBILL_PASSWORD", ""),
            totp_secret=os.getenv("TOTP_SECRET", ""),
            google_maps_api_key=os.getenv("GOOGLE_MAPS_API_KEY", ""),
            google_sheets_credentials_path=os.getenv("GOOGLE_SHEETS_CREDENTIALS_PATH", "credentials.json"),
            google_sheets_id=os.getenv("GOOGLE_SHEETS_ID", ""),
            telegram_bot_token=os.getenv("TELEGRAM_BOT_TOKEN", ""),
            telegram_chat_id=os.getenv("TELEGRAM_CHAT_ID", ""),
            headless=os.getenv("HEADLESS", "true").lower() == "true",
            browser_timeout=int(os.getenv("BROWSER_TIMEOUT", "30000")),
            max_retries=int(os.getenv("MAX_RETRIES", "5")),
            retry_delay=float(os.getenv("RETRY_DELAY", "1.0")),
            timezone=os.getenv("TIMEZONE", "Europe/Bucharest"),
        )
    
    def validate(self) -> None:
        """Validate that all required configuration is present."""
        required_fields = [
            "smartbill_username",
            "smartbill_password",
            "totp_secret",
            "google_maps_api_key",
            "google_sheets_id",
        ]

        missing_fields = []
        for field in required_fields:
            if not getattr(self, field):
                missing_fields.append(field.upper())

        if missing_fields:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_fields)}")

    def has_telegram_config(self) -> bool:
        """Check if Telegram configuration is available."""
        return bool(self.telegram_bot_token and self.telegram_chat_id)
