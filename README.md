# SmartBill e-Invoice Error Correction Automation

An automated Python solution that runs nightly to correct e-Invoice errors in SmartBill by standardizing client addresses using Google Maps geocoding, with logging to Google Sheets and Telegram notifications.

## 🚀 Features

- **Automated Login**: Handles SmartBill Cloud authentication including MFA/TOTP
- **e-Invoice Error Detection**: Automatically filters for invoices with e-Invoice validation errors
- **Address Standardization**: Uses Google Maps Geocoding API to standardize client addresses
- **Batch Processing**: Processes multiple e-Invoice error corrections efficiently
- **Comprehensive Logging**: Records all changes to Google Sheets
- **Smart Notifications**: Telegram alerts for success/failure with detailed summaries
- **Robust Error Handling**: Retry logic with exponential backoff
- **Scheduled Execution**: Runs automatically at midnight Europe/Bucharest time

## 📋 Prerequisites

Before setting up the automation, you'll need:

1. **SmartBill Account** with MFA enabled
2. **Google Cloud Project** with Maps and Sheets APIs enabled
3. **Telegram Bot** for notifications
4. **GitHub Repository** for hosting and scheduling

## 🛠️ Setup Guide

### 1. <PERSON><PERSON> and Install

```bash
git clone <your-repo-url>
cd auto-smartbill
pip install -r requirements.txt
playwright install chromium
```

### 2. SmartBill Setup

1. **Enable MFA**: Go to SmartBill account settings and enable two-factor authentication
2. **Get TOTP Secret**: When setting up the authenticator app, save the secret key (usually a QR code can be converted to text)
3. **Note Credentials**: Save your SmartBill username and password

### 3. Google Cloud Setup

#### Google Maps API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the **Geocoding API**
4. Create an API key and restrict it to Geocoding API
5. Save the API key

#### Google Sheets API
1. In the same Google Cloud project, enable the **Google Sheets API**
2. Create a **Service Account**:
   - Go to IAM & Admin → Service Accounts
   - Click "Create Service Account"
   - Give it a name like "smartbill-automation"
   - Download the JSON credentials file
3. Create a **Google Sheets spreadsheet**:
   - Create a new spreadsheet in Google Sheets
   - Share it with the service account email (found in the JSON file)
   - Give it "Editor" permissions
   - Copy the spreadsheet ID from the URL

### 4. Telegram Bot Setup

1. **Create Bot**:
   - Message [@BotFather](https://t.me/botfather) on Telegram
   - Send `/newbot` and follow instructions
   - Save the bot token

2. **Get Chat ID**:
   - Start a chat with your bot
   - Send a message to your bot
   - Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - Find your chat ID in the response

### 5. Environment Configuration

1. Copy the template: `cp .env.template .env`
2. Fill in all the values in `.env`:

```env
# SmartBill Credentials
SMARTBILL_USERNAME=your_username
SMARTBILL_PASSWORD=your_password
TOTP_SECRET=your_authenticator_secret

# Google APIs
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_SHEETS_CREDENTIALS_PATH=credentials.json
GOOGLE_SHEETS_ID=your_spreadsheet_id

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

3. Place your Google Sheets credentials JSON file as `credentials.json`

### 6. GitHub Secrets Setup

For automated execution, add these secrets to your GitHub repository:

1. Go to your repository → Settings → Secrets and variables → Actions
2. Add the following secrets:

| Secret Name | Description |
|-------------|-------------|
| `SMARTBILL_USERNAME` | Your SmartBill username |
| `SMARTBILL_PASSWORD` | Your SmartBill password |
| `TOTP_SECRET` | Your authenticator secret key |
| `GOOGLE_MAPS_API_KEY` | Google Maps API key |
| `GOOGLE_SHEETS_ID` | Google Sheets spreadsheet ID |
| `GOOGLE_SHEETS_CREDENTIALS` | Contents of your service account JSON file |
| `TELEGRAM_BOT_TOKEN` | Telegram bot token |
| `TELEGRAM_CHAT_ID` | Your Telegram chat ID |

## 🧪 Testing

### Local Testing

```bash
# Test with visible browser (for debugging)
HEADLESS=false python -m src.main

# Test with headless browser
python -m src.main
```

### Test Individual Components

```bash
# Test Telegram notifications
python -c "
import asyncio
from src.config import Config
from src.telegram_notifier import TelegramNotifier

async def test():
    config = Config.from_env()
    notifier = TelegramNotifier(config)
    await notifier.test_connection()

asyncio.run(test())
"
```

## 📅 Scheduling

The automation runs automatically via GitHub Actions at midnight Europe/Bucharest time. You can also:

1. **Manual Trigger**: Go to Actions tab → SmartBill Address Correction Automation → Run workflow
2. **Modify Schedule**: Edit `.github/workflows/smartbill-automation.yml` and change the cron expression

## 📊 Monitoring

### Google Sheets Log

The automation logs all activities to your Google Sheets with columns:
- Timestamp
- Client ID  
- Old Address/City/County
- New Address/City/County
- Status (SUCCESS/FAILED/SKIPPED)
- Error Message

### Telegram Notifications

You'll receive notifications for:
- ✅ Successful completion with statistics
- ⚠️ Partial success (some failures)
- ❌ Complete failure with error details
- 🔄 Retry attempts (for persistent issues)

## 🔧 Troubleshooting

### Common Issues

1. **Login Failures**:
   - Verify SmartBill credentials
   - Check if TOTP secret is correct
   - Ensure MFA is properly configured

2. **Geocoding Issues**:
   - Verify Google Maps API key
   - Check API quotas and billing
   - Ensure Geocoding API is enabled

3. **Sheets Logging Failures**:
   - Verify service account has access to the spreadsheet
   - Check if Google Sheets API is enabled
   - Ensure credentials JSON is valid

4. **Telegram Notifications Not Working**:
   - Verify bot token is correct
   - Check if chat ID is correct
   - Ensure you've started a conversation with the bot

### Debug Mode

Run with debug logging:

```bash
export LOG_LEVEL=DEBUG
python -m src.main
```

## 🔒 Security Notes

- Never commit `.env` files or credentials to version control
- Use GitHub Secrets for all sensitive information
- Regularly rotate API keys and passwords
- Monitor API usage and costs
- Review Google Sheets access permissions periodically

## 🏗️ Project Structure

```
auto-smartbill/
├── src/
│   ├── __init__.py
│   ├── config.py              # Configuration management
│   ├── smartbill_automation.py # SmartBill web automation
│   ├── geocoding_service.py   # Google Maps integration
│   ├── sheets_logger.py       # Google Sheets logging
│   ├── telegram_notifier.py   # Telegram notifications
│   └── main.py               # Main workflow orchestrator
├── .github/
│   └── workflows/
│       └── smartbill-automation.yml # GitHub Actions workflow
├── requirements.txt          # Python dependencies
├── .env.template            # Environment variables template
├── .gitignore              # Git ignore rules
└── README.md               # This file
```

## 🔄 Workflow Process

1. **Authentication**: Login to SmartBill Cloud with MFA support
2. **Navigation**: Navigate to Reports → Invoices and filter for "e-Facturi cu eroare"
3. **Data Extraction**: Fetch all invoices with e-Invoice validation errors
4. **Geocoding**: Standardize client addresses using Google Maps API
5. **Updates**: Apply address corrections back to SmartBill
6. **Logging**: Record all changes to Google Sheets
7. **Notification**: Send summary via Telegram

## 📈 Performance Considerations

- **Caching**: Geocoding results are cached during execution
- **Rate Limiting**: Built-in delays to respect API limits
- **Batch Operations**: Efficient processing of multiple records
- **Timeout Handling**: Configurable timeouts for web operations
- **Memory Management**: Optimized for long-running processes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review GitHub Issues
3. Create a new issue with detailed information

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.
