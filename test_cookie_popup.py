#!/usr/bin/env python3
"""
Test script to debug cookie popup handling.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config
from src.smartbill_automation import SmartBillAutomation


async def test_cookie_popup():
    """Test cookie popup detection and handling."""
    
    print("🍪 Testing Cookie Popup Handling")
    print("=" * 50)
    
    try:
        # Load configuration
        config = Config.from_env()
        config.headless = False  # Force visible browser for debugging
        
        # Create automation instance
        automation = SmartBillAutomation(config)
        
        async with automation:
            print("📱 Opening SmartBill login page...")
            
            # Navigate to login page
            await automation.page.goto("https://cloud.smartbill.ro/auth/login/")
            await automation.page.wait_for_load_state('domcontentloaded')
            
            print("⏳ Waiting for page to fully load...")
            await asyncio.sleep(3)
            
            # Check for cookie popup elements
            print("🔍 Checking for cookie popup elements...")
            
            cookie_selectors = [
                '#onetrust-accept-btn-handler',
                'button:has-text("Accept toate cookie-urile")',
                '.onetrust-close-btn-handler',
                '[id*="onetrust"]',
                '[class*="onetrust"]'
            ]
            
            for selector in cookie_selectors:
                try:
                    element = await automation.page.query_selector(selector)
                    if element:
                        is_visible = await element.is_visible()
                        text = await element.inner_text() if is_visible else "N/A"
                        print(f"✅ Found: {selector}")
                        print(f"   Visible: {is_visible}")
                        print(f"   Text: {text}")
                        
                        if is_visible:
                            print(f"🖱️  Clicking cookie button...")
                            await element.click()
                            print(f"✅ Successfully clicked!")
                            await asyncio.sleep(2)
                            break
                    else:
                        print(f"❌ Not found: {selector}")
                except Exception as e:
                    print(f"❌ Error with {selector}: {e}")
            
            print("\n🔍 Checking page content...")
            title = await automation.page.title()
            print(f"Page title: {title}")
            
            # Wait a bit to see the result
            print("\n⏳ Waiting 10 seconds for you to observe the page...")
            await asyncio.sleep(10)
            
        print("✅ Test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_cookie_popup())
