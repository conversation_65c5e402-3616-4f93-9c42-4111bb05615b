# Detailed Setup Guide for SmartBill Address Correction Automation

This guide provides step-by-step instructions for setting up the SmartBill address correction automation from scratch.

## 📋 Prerequisites Checklist

Before starting, ensure you have:

- [ ] SmartBill account with admin access
- [ ] Google account for Google Cloud services
- [ ] Telegram account
- [ ] GitHub account
- [ ] Basic familiarity with environment variables and API keys

## 🔐 Step 1: SmartBill MFA Setup

### 1.1 Enable Two-Factor Authentication

1. **Login to SmartBill**:
   - Go to https://smartbill.ro/login
   - Login with your credentials

2. **Navigate to Security Settings**:
   - Go to Account Settings → Security
   - Look for "Two-Factor Authentication" or "Autentificare în doi pași"

3. **Enable MFA**:
   - Click "Enable" or "Activează"
   - Choose "Authenticator App" option

4. **Get the Secret Key**:
   - When the QR code appears, look for a "Can't scan?" or "Manual entry" link
   - Click it to reveal the secret key (a long string of letters and numbers)
   - **IMPORTANT**: Save this secret key - you'll need it for `TOTP_SECRET`

5. **Complete Setup**:
   - Use any authenticator app (Google Authenticator, Authy, etc.) to scan the QR code
   - Enter the verification code to complete setup

### 1.2 Test MFA

- Logout and login again to ensure MFA is working
- Note down your username and password for later use

## 🌍 Step 2: Google Cloud Platform Setup

### 2.1 Create Google Cloud Project

1. **Go to Google Cloud Console**:
   - Visit https://console.cloud.google.com/
   - Sign in with your Google account

2. **Create New Project**:
   - Click "Select a project" → "New Project"
   - Name: "SmartBill Automation" (or your preferred name)
   - Click "Create"

3. **Enable Billing** (if not already done):
   - Go to Billing → Link a billing account
   - Note: Google provides free credits for new accounts

### 2.2 Enable Required APIs

1. **Navigate to APIs & Services**:
   - Go to APIs & Services → Library

2. **Enable Geocoding API**:
   - Search for "Geocoding API"
   - Click on it and press "Enable"

3. **Enable Google Sheets API**:
   - Search for "Google Sheets API"
   - Click on it and press "Enable"

### 2.3 Create Google Maps API Key

1. **Go to Credentials**:
   - APIs & Services → Credentials

2. **Create API Key**:
   - Click "Create Credentials" → "API Key"
   - Copy the generated key

3. **Restrict the API Key**:
   - Click on the key to edit it
   - Under "API restrictions", select "Restrict key"
   - Choose "Geocoding API"
   - Save the changes

### 2.4 Create Service Account for Google Sheets

1. **Create Service Account**:
   - Go to IAM & Admin → Service Accounts
   - Click "Create Service Account"
   - Name: "smartbill-automation"
   - Description: "Service account for SmartBill address automation"
   - Click "Create and Continue"

2. **Skip Role Assignment** (click "Continue")

3. **Create Key**:
   - Click on the created service account
   - Go to "Keys" tab
   - Click "Add Key" → "Create new key"
   - Choose "JSON" format
   - Download the file and save it as `credentials.json`

### 2.5 Create Google Sheets Spreadsheet

1. **Create New Spreadsheet**:
   - Go to https://sheets.google.com/
   - Click "Blank" to create new spreadsheet
   - Name it "SmartBill Address Corrections Log"

2. **Share with Service Account**:
   - Click "Share" button
   - Add the service account email (found in the credentials.json file)
   - Give it "Editor" permissions
   - Uncheck "Notify people"

3. **Get Spreadsheet ID**:
   - Copy the ID from the URL: `https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit`
   - Save this ID for later

## 🤖 Step 3: Telegram Bot Setup

### 3.1 Create Telegram Bot

1. **Start Chat with BotFather**:
   - Open Telegram and search for "@BotFather"
   - Start a conversation

2. **Create New Bot**:
   - Send `/newbot`
   - Choose a name for your bot (e.g., "SmartBill Automation Bot")
   - Choose a username (must end with "bot", e.g., "smartbill_automation_bot")

3. **Save Bot Token**:
   - BotFather will provide a token like `*********:ABCdefGHIjklMNOpqrsTUVwxyz`
   - Save this token securely

### 3.2 Get Your Chat ID

1. **Start Chat with Your Bot**:
   - Click the link provided by BotFather to start chat with your bot
   - Send any message (e.g., "Hello")

2. **Get Chat ID**:
   - Open this URL in browser: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - Replace `<YOUR_BOT_TOKEN>` with your actual bot token
   - Look for `"chat":{"id":*********}` in the response
   - The number is your chat ID (can be negative)

## 🔧 Step 4: Local Development Setup

### 4.1 Clone Repository

```bash
git clone <your-repository-url>
cd auto-smartbill
```

### 4.2 Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate it
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 4.3 Install Dependencies

```bash
pip install -r requirements.txt
playwright install chromium
```

### 4.4 Configure Environment

1. **Copy Template**:
   ```bash
   cp .env.template .env
   ```

2. **Edit .env File**:
   ```env
   # SmartBill Credentials
   SMARTBILL_USERNAME=your_smartbill_username
   SMARTBILL_PASSWORD=your_smartbill_password
   TOTP_SECRET=your_authenticator_secret_from_step_1

   # Google APIs
   GOOGLE_MAPS_API_KEY=your_google_maps_api_key_from_step_2
   GOOGLE_SHEETS_CREDENTIALS_PATH=credentials.json
   GOOGLE_SHEETS_ID=your_spreadsheet_id_from_step_2

   # Telegram
   TELEGRAM_BOT_TOKEN=your_bot_token_from_step_3
   TELEGRAM_CHAT_ID=your_chat_id_from_step_3

   # Optional settings (can leave as default)
   HEADLESS=true
   BROWSER_TIMEOUT=30000
   MAX_RETRIES=5
   RETRY_DELAY=1.0
   TIMEZONE=Europe/Bucharest
   ```

3. **Place Credentials File**:
   - Copy the `credentials.json` file from Step 2.4 to the project root

### 4.5 Test the Setup

```bash
# Test with visible browser (for debugging)
HEADLESS=false python -m src.main

# If everything works, test in headless mode
python -m src.main
```

## 🚀 Step 5: GitHub Actions Setup

### 5.1 Push Code to GitHub

```bash
git add .
git commit -m "Initial SmartBill automation setup"
git push origin main
```

### 5.2 Configure GitHub Secrets

1. **Go to Repository Settings**:
   - Navigate to your GitHub repository
   - Click Settings → Secrets and variables → Actions

2. **Add Repository Secrets**:
   Click "New repository secret" for each:

   | Secret Name | Value |
   |-------------|-------|
   | `SMARTBILL_USERNAME` | Your SmartBill username |
   | `SMARTBILL_PASSWORD` | Your SmartBill password |
   | `TOTP_SECRET` | Your authenticator secret |
   | `GOOGLE_MAPS_API_KEY` | Your Google Maps API key |
   | `GOOGLE_SHEETS_ID` | Your Google Sheets spreadsheet ID |
   | `GOOGLE_SHEETS_CREDENTIALS` | **Entire contents** of credentials.json file |
   | `TELEGRAM_BOT_TOKEN` | Your Telegram bot token |
   | `TELEGRAM_CHAT_ID` | Your Telegram chat ID |

### 5.3 Test GitHub Actions

1. **Manual Trigger**:
   - Go to Actions tab in your repository
   - Click "SmartBill Address Correction Automation"
   - Click "Run workflow"
   - Choose "false" for headless to see browser actions (for testing)

2. **Check Logs**:
   - Monitor the workflow execution
   - Check for any errors in the logs

## ✅ Step 6: Verification

### 6.1 Test All Components

1. **Telegram Notifications**: You should receive a start notification
2. **SmartBill Login**: Check logs for successful authentication
3. **Google Sheets**: Verify data is being logged
4. **Address Corrections**: Check if addresses are being updated

### 6.2 Schedule Verification

The automation will run automatically at midnight Europe/Bucharest time. To verify:

1. **Check Cron Schedule**: The workflow runs at `0 22 * * *` UTC (adjust for daylight saving)
2. **Monitor First Run**: Check the next day for execution logs
3. **Verify Notifications**: Ensure you receive Telegram updates

## 🔍 Troubleshooting

### Common Setup Issues

1. **"Invalid TOTP Secret"**:
   - Ensure you copied the secret key correctly from SmartBill MFA setup
   - The secret should be a long string without spaces

2. **"Google Sheets Permission Denied"**:
   - Verify the service account email has Editor access to the spreadsheet
   - Check that the spreadsheet ID is correct

3. **"Telegram Bot Not Responding"**:
   - Ensure you've started a conversation with the bot
   - Verify the chat ID is correct (can be negative)

4. **"Google Maps API Quota Exceeded"**:
   - Check your Google Cloud billing account
   - Verify the API key has proper restrictions

### Getting Help

If you encounter issues:

1. Check the GitHub Actions logs for detailed error messages
2. Review the troubleshooting section in README.md
3. Create an issue in the repository with:
   - Error messages (remove sensitive information)
   - Steps you've already tried
   - Your environment details

## 🎉 Congratulations!

You've successfully set up the SmartBill address correction automation! The system will now:

- Run automatically every night at midnight
- Correct client addresses using Google Maps
- Log all changes to Google Sheets
- Send you Telegram notifications with results

Remember to monitor the first few runs to ensure everything works as expected.
