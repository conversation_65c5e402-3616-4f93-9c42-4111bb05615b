#!/usr/bin/env python3
"""
Test script to demonstrate Google Maps sector detection for Bucharest addresses.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config
from src.geocoding_service import GeocodingService


async def test_bucharest_addresses():
    """Test sector detection for various Bucharest addresses."""
    
    # Load configuration
    try:
        config = Config.from_env()
        geocoding = GeocodingService(config)
    except Exception as e:
        print(f"Error loading configuration: {e}")
        print("Make sure you have a .env file with GOOGLE_MAPS_API_KEY")
        return
    
    # Test addresses in different Bucharest sectors
    test_addresses = [
        ("Calea Victoriei 15", "Bucuresti", "Bucuresti"),
        ("Bulevardul Stefan cel Mare 20", "Bucuresti", "Bucuresti"), 
        ("Piața Unirii 1", "Bucuresti", "Bucuresti"),
        ("Calea Serban Voda 10", "Bucuresti", "Bucuresti"),
        ("Drumul Taberei 25", "Bucuresti", "Bucuresti"),
        ("Calea Giulesti 30", "Bucuresti", "Bucuresti"),
        ("Strada Amzei 5", "Bucuresti", "Bucuresti"),
        ("Bulevardul Unirii 50", "Bucuresti", "Bucuresti"),
    ]
    
    print("🗺️  Testing Google Maps Bucharest Sector Detection")
    print("=" * 60)
    
    for i, (address, city, county) in enumerate(test_addresses, 1):
        print(f"\n{i}. Testing: {address}, {city}, {county}")
        
        try:
            # Geocode the address
            new_address, new_city, new_county = await geocoding.geocode_address(address, city, county)
            
            if new_address or new_city or new_county:
                print(f"   ✅ Original: {address}, {city}, {county}")
                print(f"   🎯 Result:   {new_address}, {new_city}, {new_county}")
                
                if new_county == "Bucuresti" and "Sector" in new_city:
                    print(f"   🏙️  Sector detected: {new_city}")
                else:
                    print(f"   ⚠️  No sector detected")
            else:
                print(f"   ❌ No geocoding results")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")


if __name__ == "__main__":
    asyncio.run(test_bucharest_addresses())
