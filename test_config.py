#!/usr/bin/env python3
"""
Test script to verify configuration works without Telegram.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config


def test_config_without_telegram():
    """Test that configuration works without Telegram credentials."""
    
    print("🔧 Testing configuration without Telegram...")
    
    try:
        # Load configuration
        config = Config.from_env()
        
        # Validate configuration (should not require Telegram now)
        config.validate()
        
        print("✅ Configuration validation passed!")
        
        # Check Telegram availability
        if config.has_telegram_config():
            print("📱 Telegram notifications: ENABLED")
            print(f"   Bot token: {config.telegram_bot_token[:10]}...")
            print(f"   Chat ID: {config.telegram_chat_id}")
        else:
            print("📱 Telegram notifications: DISABLED (no credentials provided)")
        
        # Show other configuration
        print(f"🔐 SmartBill user: {config.smartbill_username}")
        print(f"🗺️  Google Maps API: {'✅ Configured' if config.google_maps_api_key else '❌ Missing'}")
        print(f"📊 Google Sheets: {'✅ Configured' if config.google_sheets_id else '❌ Missing'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


if __name__ == "__main__":
    success = test_config_without_telegram()
    sys.exit(0 if success else 1)
