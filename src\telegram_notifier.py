"""Telegram notification service for SmartBill automation."""

import asyncio
import logging
from typing import List, Optional

try:
    from telegram import <PERSON><PERSON>
    from telegram.error import TelegramError
    TELEGRAM_AVAILABLE = True
except ImportError:
    try:
        from telegram.ext import <PERSON><PERSON>
        from telegram.error import TelegramError
        TELEGRAM_AVAILABLE = True
    except ImportError:
        # Fallback for when telegram is not available
        TELEGRAM_AVAILABLE = False
        Bot = None
        TelegramError = Exception

from .config import Config

logger = logging.getLogger(__name__)


class TelegramNotifier:
    """Handles Telegram notifications for the SmartBill automation."""

    def __init__(self, config: Config):
        self.config = config
        if not TELEGRAM_AVAILABLE:
            raise ImportError("Telegram library is not available")
        self.bot = Bot(token=config.telegram_bot_token)
        self.chat_id = config.telegram_chat_id
    
    async def send_message(self, message: str, parse_mode: str = "HTML") -> bool:
        """
        Send a message to the configured Telegram chat.
        
        Args:
            message: Message text to send
            parse_mode: Parse mode for the message (HTML, Markdown, or None)
            
        Returns:
            bool: True if message sent successfully, False otherwise
        """
        try:
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=parse_mode
            )
            logger.debug("Telegram message sent successfully")
            return True
            
        except TelegramError as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending Telegram message: {e}")
            return False
    
    async def notify_start(self) -> bool:
        """
        Send notification that the automation process has started.

        Returns:
            bool: True if notification sent successfully, False otherwise
        """
        message = (
            "🚀 <b>SmartBill e-Invoice Error Correction Started</b>\n\n"
            "The automated e-Invoice error correction process has begun.\n"
            "You will receive updates on completion or if any errors occur."
        )
        return await self.send_message(message)
    
    async def notify_success(self, total_records: int, successful: int, failed: int, skipped: int, sheets_url: str) -> bool:
        """
        Send success notification with summary statistics.
        
        Args:
            total_records: Total number of records processed
            successful: Number of successful corrections
            failed: Number of failed corrections
            skipped: Number of skipped records
            sheets_url: URL to the Google Sheets log
            
        Returns:
            bool: True if notification sent successfully, False otherwise
        """
        success_rate = (successful / total_records * 100) if total_records > 0 else 0
        
        message = (
            "✅ <b>SmartBill e-Invoice Error Correction Completed</b>\n\n"
            f"📊 <b>Summary:</b>\n"
            f"• Total records: {total_records}\n"
            f"• Successfully corrected: {successful}\n"
            f"• Failed: {failed}\n"
            f"• Skipped: {skipped}\n"
            f"• Success rate: {success_rate:.1f}%\n\n"
            f"📋 <a href='{sheets_url}'>View detailed log</a>"
        )
        
        return await self.send_message(message)
    
    async def notify_failure(self, error_message: str, total_records: int = 0, processed: int = 0, sheets_url: str = "") -> bool:
        """
        Send failure notification with error details.
        
        Args:
            error_message: Description of the error
            total_records: Total number of records that were supposed to be processed
            processed: Number of records processed before failure
            sheets_url: URL to the Google Sheets log (if available)
            
        Returns:
            bool: True if notification sent successfully, False otherwise
        """
        message = (
            "❌ <b>SmartBill e-Invoice Error Correction Failed</b>\n\n"
            f"🚨 <b>Error:</b> {error_message}\n\n"
        )
        
        if total_records > 0:
            message += (
                f"📊 <b>Progress before failure:</b>\n"
                f"• Records processed: {processed}/{total_records}\n\n"
            )
        
        if sheets_url:
            message += f"📋 <a href='{sheets_url}'>View partial log</a>\n\n"
        
        message += "Please check the logs and retry the process."
        
        return await self.send_message(message)
    
    async def notify_retry(self, attempt: int, max_attempts: int, error_message: str) -> bool:
        """
        Send notification about retry attempts.
        
        Args:
            attempt: Current attempt number
            max_attempts: Maximum number of attempts
            error_message: Error that triggered the retry
            
        Returns:
            bool: True if notification sent successfully, False otherwise
        """
        message = (
            f"🔄 <b>SmartBill Automation Retry</b>\n\n"
            f"Attempt {attempt}/{max_attempts}\n"
            f"Reason: {error_message}\n\n"
            "Retrying in a few moments..."
        )
        
        return await self.send_message(message)
    
    async def notify_partial_success(self, successful: int, failed: int, errors: List[str], sheets_url: str) -> bool:
        """
        Send notification for partial success (some records failed).
        
        Args:
            successful: Number of successful corrections
            failed: Number of failed corrections
            errors: List of error messages
            sheets_url: URL to the Google Sheets log
            
        Returns:
            bool: True if notification sent successfully, False otherwise
        """
        total = successful + failed
        success_rate = (successful / total * 100) if total > 0 else 0
        
        message = (
            "⚠️ <b>SmartBill e-Invoice Error Correction Completed with Issues</b>\n\n"
            f"📊 <b>Summary:</b>\n"
            f"• Successfully corrected: {successful}\n"
            f"• Failed: {failed}\n"
            f"• Success rate: {success_rate:.1f}%\n\n"
        )
        
        if errors:
            # Show first few errors
            error_preview = errors[:3]
            message += f"🚨 <b>Sample errors:</b>\n"
            for i, error in enumerate(error_preview, 1):
                message += f"{i}. {error[:100]}{'...' if len(error) > 100 else ''}\n"
            
            if len(errors) > 3:
                message += f"... and {len(errors) - 3} more errors\n"
            
            message += "\n"
        
        message += f"📋 <a href='{sheets_url}'>View detailed log</a>"
        
        return await self.send_message(message)
    
    async def notify_no_records(self) -> bool:
        """
        Send notification when no address correction records are found.
        
        Returns:
            bool: True if notification sent successfully, False otherwise
        """
        message = (
            "ℹ️ <b>SmartBill e-Invoice Error Correction</b>\n\n"
            "No e-Invoice error records found.\n"
            "All invoices appear to be valid! ✨"
        )
        
        return await self.send_message(message)
    
    async def test_connection(self) -> bool:
        """
        Test the Telegram bot connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            bot_info = await self.bot.get_me()
            logger.info(f"Telegram bot connection successful: @{bot_info.username}")
            return True
        except Exception as e:
            logger.error(f"Telegram bot connection failed: {e}")
            return False
