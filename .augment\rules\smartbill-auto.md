---
type: "agent_requested"
description: "Example description"
---
You are an expert in automating web-based workflows with Python, focusing on modern JS frameworks and reliable scheduling.

Key Principles
Concise, technical guidance with clear Python examples.

Readable, efficient, maintainable code—favor modularity and reusable functions.

PEP 8–compliant style throughout.

Secure by design: credentials, API keys, and MFA secrets only via .env or CI secrets.

Authentication & Navigation
Playwright (or Selenium if preferred) to handle Vue.js/Preact login flows.

MFA support: generate TOTP codes programmatically (e.g. pyotp) from your stored seed.

Headless mode for CI runs, with optional verbose (headed) mode for local debugging.

Explicit waits on selectors to ensure stability in dynamic content.

Address-Correction Workflow
Locate the “Address corrections” pop-up in Clients → Corrections.

Extract the raw “Adresa”, “Localitate”, “Județ” fields using Playwright page queries.

Normalize input strings (trim, unify diacritics) before geocoding.

Invoke Google Maps Geocoding API (googlemaps Python client):

Build address query from raw fields.

Parse returned components into “Adresa”, “Localitate”, “Județ.”

Populate & save corrected fields back into SmartBill and confirm success before proceeding.

Scheduling & Deployment
GitHub Actions workflow triggered at 0 0 * * * Europe/Bucharest:

Uses a matrix runner only if branching for testing vs. production.

Secrets (SMARTBILL_USER, SMARTBILL_PASS, TOTP_SECRET, GOOGLE_API_KEY, TELEGRAM_TOKEN, etc.) stored in repo’s Settings → Secrets.

Local preview: a simple CLI switch (--local) kicks off a headed debug run.

Logging & Notifications
Google Sheets via google-api-python-client service account:

Append rows: timestamp, client ID, old vs. new address, status.

Telegram Bot API for run summaries and error alerts:

On success: “✅ Address fix completed: N records updated.”

On failure or retries exhausted: “❌ Error on client X: <error>.”

Log rotation: keep only last 30 days of logs (via sheet retention policy).

Error Handling & Retry Logic
Retry up to 5× with exponential backoff for:

Login/timeouts (TimeoutError).

Element-not-found (PlaywrightTimeoutError).

API quota or network errors (googlemaps.exceptions.*).

Catch-all guard around the main loop to ensure the run always ends by sending a Telegram notification.

Performance & Maintainability
Modular functions:

login(), fetch_corrections(), geocode(address), apply_correction(record), log_change(...), notify(...).

Async where possible (Playwright’s async API) to speed up multi-record processing.

Caching: optional requests-cache layer on Google Maps calls to avoid duplicate queries in dev.

Profiling hooks: include a simple flag to dump cProfile stats for any run.

Dependencies
text
Copy
Edit
playwright
pyotp
python-dotenv
google-api-python-client
googlemaps
telegram
requests-cache