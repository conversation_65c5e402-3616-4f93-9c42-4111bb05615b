#!/usr/bin/env python3
"""
Local runner script for SmartBill automation with debugging options.
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import Config
from src.main import WorkflowOrchestrator


def setup_logging(debug: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if debug else logging.INFO

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('smartbill_automation_local.log', encoding='utf-8')
        ]
    )

    # Set encoding for stdout to handle Unicode characters
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')


async def test_components(config: Config):
    """Test individual components."""
    from src.telegram_notifier import TelegramNotifier
    from src.sheets_logger import SheetsLogger
    from src.geocoding_service import GeocodingService
    
    print("🧪 Testing components...")
    
    # Test Telegram
    print("📱 Testing Telegram bot...")
    notifier = TelegramNotifier(config)
    if await notifier.test_connection():
        print("✅ Telegram bot connection successful")
    else:
        print("❌ Telegram bot connection failed")
    
    # Test Google Sheets
    print("📊 Testing Google Sheets...")
    sheets = SheetsLogger(config)
    if sheets.authenticate():
        print("✅ Google Sheets authentication successful")
        print(f"📋 Spreadsheet URL: {sheets.get_spreadsheet_url()}")
    else:
        print("❌ Google Sheets authentication failed")
    
    # Test Geocoding
    print("🗺️ Testing Google Maps geocoding...")
    geocoding = GeocodingService(config)
    try:
        result = await geocoding.geocode_address("Strada Victoriei 1", "București", "București")
        if any(result):
            print(f"✅ Geocoding successful: {result}")
        else:
            print("⚠️ Geocoding returned no results")
    except Exception as e:
        print(f"❌ Geocoding failed: {e}")


async def main():
    """Main entry point for local testing."""
    parser = argparse.ArgumentParser(description="SmartBill Address Correction Automation - Local Runner")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--headless", action="store_true", help="Run browser in headless mode")
    parser.add_argument("--test-only", action="store_true", help="Only test components, don't run full workflow")
    parser.add_argument("--dry-run", action="store_true", help="Run workflow but don't make actual changes")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.debug)
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = Config.from_env()
        
        # Override headless setting if specified, otherwise use config value
        if args.headless:
            config.headless = True
        # If no --headless flag, keep the original config value from .env
        
        # Validate configuration
        config.validate()
        logger.info("Configuration loaded and validated successfully")
        
        if args.test_only:
            # Test components only
            await test_components(config)
        else:
            # Run full workflow
            logger.info("Starting SmartBill automation workflow...")
            
            if args.dry_run:
                logger.info("🔍 DRY RUN MODE - No actual changes will be made")
                # You could implement dry-run logic here
            
            orchestrator = WorkflowOrchestrator(config)
            success = await orchestrator.run_workflow()
            
            if success:
                logger.info("Workflow completed successfully")
                sys.exit(0)
            else:
                logger.error("Workflow failed")
                sys.exit(1)
                
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
