"""
Data models for SmartBill automation.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class AddressRecord:
    """
    Represents a client address record from SmartBill invoice.
    
    Attributes:
        client_id: Unique identifier for the client
        client_name: Name of the client
        invoice_id: SmartBill invoice ID
        current_address: Current address from invoice
        current_city: Current city from invoice
        current_county: Current county from invoice
        new_address: Geocoded/corrected address (optional)
        new_city: Geocoded/corrected city (optional)
        new_county: Geocoded/corrected county (optional)
    """
    client_id: str
    client_name: str
    invoice_id: str
    current_address: str = ""
    current_city: str = ""
    current_county: str = ""
    new_address: Optional[str] = None
    new_city: Optional[str] = None
    new_county: Optional[str] = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        # Ensure client_id and client_name are not empty
        if not self.client_id:
            self.client_id = self.client_name or f"client_{self.invoice_id}"
        if not self.client_name:
            self.client_name = self.client_id
    
    def has_address_change(self) -> bool:
        """
        Check if the address has been changed from current to new.
        
        Returns:
            bool: True if any address component has changed
        """
        return (
            (self.new_address and self.new_address != self.current_address) or
            (self.new_city and self.new_city != self.current_city) or
            (self.new_county and self.new_county != self.current_county)
        )
    
    def get_current_full_address(self) -> str:
        """
        Get the current full address as a single string.
        
        Returns:
            str: Formatted current address
        """
        parts = [
            self.current_address,
            self.current_city,
            self.current_county
        ]
        return ", ".join(part for part in parts if part)
    
    def get_new_full_address(self) -> str:
        """
        Get the new full address as a single string.
        
        Returns:
            str: Formatted new address
        """
        parts = [
            self.new_address or self.current_address,
            self.new_city or self.current_city,
            self.new_county or self.current_county
        ]
        return ", ".join(part for part in parts if part)
    
    def to_dict(self) -> dict:
        """
        Convert the record to a dictionary for logging/export.
        
        Returns:
            dict: Dictionary representation of the record
        """
        return {
            'client_id': self.client_id,
            'client_name': self.client_name,
            'invoice_id': self.invoice_id,
            'current_address': self.current_address,
            'current_city': self.current_city,
            'current_county': self.current_county,
            'new_address': self.new_address,
            'new_city': self.new_city,
            'new_county': self.new_county,
            'has_changes': self.has_address_change()
        }
