# 🚀 SmartBill Automation - GitHub Actions Deployment Guide

This comprehensive guide will help you deploy the SmartBill automation system to GitHub Actions for automated execution.

## 📁 File Structure

Your repository should have the following structure:

```
auto-smartbill/
├── .github/
│   └── workflows/
│       └── smartbill-automation.yml    # Main workflow file
├── src/                                 # Source code directory
│   ├── main.py                         # Main automation script
│   ├── smartbill_automation.py         # SmartBill automation logic
│   ├── geocoding_service.py            # Google Maps geocoding
│   ├── sheets_logger.py                # Google Sheets logging
│   ├── config.py                       # Configuration management
│   └── models.py                       # Data models
├── requirements.txt                     # Python dependencies
├── run_local.py                        # Local execution script
└── README.md                           # Project documentation
```

## 🔐 Step 1: Configure Repository Secrets

Navigate to your GitHub repository → Settings → Secrets and variables → Actions

### Required Secrets:

#### **SmartBill Credentials**
```
SMARTBILL_USERNAME          # Your SmartBill login username
SMARTBILL_PASSWORD          # Your SmartBill login password
SMARTBILL_MFA_SECRET        # TOTP secret for 2FA (if enabled)
```

#### **Google APIs**
```
GOOGLE_MAPS_API_KEY         # Google Maps API key for geocoding
GOOGLE_SHEETS_CREDENTIALS   # Google Sheets service account JSON
GOOGLE_SHEETS_ID            # Target Google Sheets document ID
```

#### **Notifications (Optional)**
```
TELEGRAM_BOT_TOKEN          # Telegram bot token for notifications
TELEGRAM_CHAT_ID            # Telegram chat ID for notifications
WEBHOOK_URL                 # Custom webhook URL for notifications
```

### Setting up Google Sheets Credentials:

1. **Create a Google Cloud Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable APIs**:
   - Enable Google Sheets API
   - Enable Google Maps Geocoding API

3. **Create Service Account**:
   - Go to IAM & Admin → Service Accounts
   - Create new service account
   - Download JSON key file

4. **Configure Sheets Access**:
   - Share your Google Sheet with the service account email
   - Give "Editor" permissions

5. **Add to GitHub Secrets**:
   - Copy the entire JSON content
   - Paste as `GOOGLE_SHEETS_CREDENTIALS` secret

## 📦 Step 2: Dependencies Configuration

Ensure your `requirements.txt` includes all necessary dependencies:

```txt
playwright>=1.40.0
googlemaps>=4.10.0
google-auth>=2.23.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.1.1
google-api-python-client>=2.100.0
pyotp>=2.9.0
requests>=2.31.0
python-telegram-bot>=20.6
asyncio>=3.4.3
```

## 🔧 Step 3: Workflow Configuration

The workflow file `.github/workflows/smartbill-automation.yml` provides:

### **Trigger Options**:
- **Manual**: `workflow_dispatch` with configurable parameters
- **Scheduled**: Daily at 9:00 AM UTC, Weekly on Mondays
- **Event-based**: On push to main/develop branches

### **Configuration Parameters**:
- `environment`: production/staging/development
- `dry_run`: Run without making actual changes
- `max_records`: Limit number of records to process
- `headless`: Browser headless mode
- `notification_level`: Control notification frequency

### **Key Features**:
- ✅ Comprehensive error handling
- ✅ Artifact upload (logs, screenshots)
- ✅ Multiple notification channels
- ✅ Environment validation
- ✅ Secure credential management

## 🚀 Step 4: Deployment Steps

### 1. **Push Code to Repository**:
```bash
git add .
git commit -m "Add GitHub Actions workflow"
git push origin main
```

### 2. **Configure Secrets**:
- Add all required secrets in repository settings
- Verify secret names match workflow expectations

### 3. **Test Manual Execution**:
- Go to Actions tab in GitHub
- Select "SmartBill Automation System"
- Click "Run workflow"
- Configure parameters and run

### 4. **Monitor Execution**:
- Check workflow logs in real-time
- Review summary reports
- Download artifacts if needed

## ⏰ Step 5: Scheduling Options

### **Daily Execution**:
```yaml
schedule:
  - cron: '0 9 * * *'  # 9:00 AM UTC daily
```

### **Weekly Execution**:
```yaml
schedule:
  - cron: '0 6 * * 1'  # 6:00 AM UTC on Mondays
```

### **Custom Schedule Examples**:
```yaml
# Every 6 hours
- cron: '0 */6 * * *'

# Weekdays only at 8 AM UTC
- cron: '0 8 * * 1-5'

# First day of month at midnight UTC
- cron: '0 0 1 * *'
```

## 🔍 Step 6: Monitoring and Debugging

### **Workflow Logs**:
- Real-time execution logs in Actions tab
- Detailed step-by-step progress
- Error messages and stack traces

### **Artifacts**:
- Automation logs
- Screenshots (if browser issues)
- Configuration files (sanitized)

### **Notifications**:
- Telegram messages for failures/success
- Custom webhook integration
- GitHub step summaries

### **Common Issues**:

1. **Browser Issues**:
   - Ensure Playwright browsers are installed
   - Check headless mode configuration
   - Review timeout settings

2. **Authentication Failures**:
   - Verify SmartBill credentials
   - Check MFA secret format
   - Validate Google API keys

3. **Permission Errors**:
   - Confirm Google Sheets sharing
   - Check service account permissions
   - Verify API quotas

## 🛡️ Step 7: Security Best Practices

### **Secrets Management**:
- Never commit secrets to repository
- Use GitHub encrypted secrets
- Rotate credentials regularly
- Limit secret access to necessary workflows

### **Environment Isolation**:
- Use different environments (prod/staging)
- Separate credentials per environment
- Test in staging before production

### **Access Control**:
- Limit repository access
- Use branch protection rules
- Require reviews for workflow changes

## 📊 Step 8: Performance Optimization

### **Resource Management**:
- Set appropriate timeouts (60-90 minutes)
- Limit concurrent executions
- Use caching for dependencies

### **Cost Optimization**:
- Use scheduled runs efficiently
- Implement dry-run mode for testing
- Monitor GitHub Actions usage

### **Reliability**:
- Implement retry mechanisms
- Add comprehensive error handling
- Use fallback strategies

## 🔄 Step 9: Maintenance

### **Regular Updates**:
- Update dependencies monthly
- Review and rotate secrets quarterly
- Monitor workflow performance

### **Monitoring**:
- Set up alerts for failures
- Track execution metrics
- Review logs regularly

### **Backup**:
- Export workflow configurations
- Backup Google Sheets data
- Document secret recovery procedures

## 📞 Step 10: Support and Troubleshooting

### **Debugging Steps**:
1. Check workflow logs in GitHub Actions
2. Review uploaded artifacts
3. Verify secret configuration
4. Test locally with same parameters
5. Check API quotas and limits

### **Common Solutions**:
- Increase timeout values for slow operations
- Update browser installation commands
- Verify environment variable names
- Check network connectivity issues

### **Getting Help**:
- Review GitHub Actions documentation
- Check Playwright troubleshooting guides
- Verify Google API status pages
- Test individual components locally

---

## ✅ Quick Start Checklist

- [ ] Repository structure is correct
- [ ] All secrets are configured
- [ ] Dependencies are up to date
- [ ] Workflow file is in place
- [ ] Manual test execution successful
- [ ] Scheduled execution configured
- [ ] Notifications are working
- [ ] Monitoring is set up
- [ ] Documentation is updated

Your SmartBill automation is now ready for production deployment on GitHub Actions! 🎉
